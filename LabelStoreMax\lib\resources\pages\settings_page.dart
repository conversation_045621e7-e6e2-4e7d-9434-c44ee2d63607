//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/app/services/auth_service.dart';
import '/resources/pages/auth_options_page.dart';
import '/resources/pages/account_profile_update_page.dart';
import '/resources/pages/wishlist_page_widget.dart';
import '/resources/widgets/safearea_widget.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:nylo_framework/theme/helper/ny_theme.dart';

class SettingsPage extends NyStatefulWidget {
  static RouteView path = ("/settings", (_) => SettingsPage());

  SettingsPage({super.key}) : super(child: () => _SettingsPageState());
}

class _SettingsPageState extends NyPage<SettingsPage> {
  bool _isLoggedIn = false;
  bool _isDarkMode = false;
  bool _notificationsEnabled = true;

  @override
  get init => () async {
    _isLoggedIn = await AuthService().isLoggedIn();
    _isDarkMode = Theme.of(context).brightness == Brightness.dark;
  };

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'الإعدادات',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).appBarTheme.titleTextStyle?.color,
          ),
        ),
        centerTitle: true,
        automaticallyImplyLeading: false,
      ),
      body: SafeAreaWidget(
        child: ListView(
          padding: EdgeInsets.all(16),
          children: [
            // General Settings Section
            _buildSectionHeader('الإعدادات العامة'),
            SizedBox(height: 8),
            
            // Login/Profile Setting
            _buildSettingsTile(
              icon: Icons.person,
              title: _isLoggedIn ? 'الملف الشخصي' : 'تسجيل الدخول',
              subtitle: _isLoggedIn ? 'إدارة معلومات الحساب' : 'قم بتسجيل الدخول للوصول إلى حسابك',
              onTap: () {
                if (_isLoggedIn) {
                  routeTo(AccountProfileUpdatePage.path);
                } else {
                  routeTo(AuthOptionsPage.path);
                }
              },
            ),
            

            
            // Wishlist Setting
            _buildSettingsTile(
              icon: Icons.favorite,
              title: 'قائمة رغباتي',
              subtitle: 'عرض وإدارة المنتجات المفضلة',
              onTap: () {
                routeTo(WishListPageWidget.path);
              },
            ),
            
            // Notifications Toggle
            _buildToggleTile(
              icon: Icons.notifications,
              title: 'تلقي الإشعارات',
              subtitle: 'تفعيل أو إلغاء الإشعارات',
              value: _notificationsEnabled,
              onChanged: (value) {
                setState(() {
                  _notificationsEnabled = value;
                });
                showToast(
                  title: value ? "تم تفعيل الإشعارات" : "تم إلغاء الإشعارات",
                  description: value ? "ستتلقى الإشعارات الآن" : "لن تتلقى إشعارات",
                  style: ToastNotificationStyleType.success,
                );
              },
            ),
            
            // Dark Mode Toggle
            _buildToggleTile(
              icon: Icons.dark_mode,
              title: 'المظهر',
              subtitle: _isDarkMode ? 'الوضع المظلم' : 'الوضع الفاتح',
              value: _isDarkMode,
              onChanged: (value) {
                setState(() {
                  _isDarkMode = value;
                });
                // Toggle theme using existing theme switching logic
                NyTheme.set(context,
                    id: value ? "default_dark_theme" : "default_light_theme");
                showToast(
                  title: "تم تغيير المظهر",
                  description: value ? "تم التبديل إلى الوضع المظلم" : "تم التبديل إلى الوضع الفاتح",
                  style: ToastNotificationStyleType.success,
                );
              },
            ),
            
            // Language Setting
            _buildSettingsTile(
              icon: Icons.language,
              title: 'اللغة',
              subtitle: 'تغيير لغة التطبيق',
              onTap: () {
                NyLanguageSwitcher.showBottomModal(context);
              },
            ),
            
            SizedBox(height: 24),
            
            // App & Support Section
            _buildSectionHeader('التطبيق والدعم'),
            SizedBox(height: 8),
            
            // Rate App Setting
            _buildSettingsTile(
              icon: Icons.star,
              title: 'تقييم التطبيق',
              subtitle: 'قيم تجربتك مع التطبيق',
              onTap: () {
                showToast(
                  title: "شكراً لك",
                  description: "سيتم توجيهك لتقييم التطبيق قريباً",
                  style: ToastNotificationStyleType.info,
                );
              },
            ),
            
            // Privacy & Terms Setting
            _buildSettingsTile(
              icon: Icons.privacy_tip,
              title: 'الخصوصية والشروط',
              subtitle: 'سياسة الخصوصية وشروط الاستخدام',
              onTap: () {
                showToast(
                  title: "قريباً",
                  description: "ستتوفر صفحة الخصوصية والشروط قريباً",
                  style: ToastNotificationStyleType.info,
                );
              },
            ),
            
            // About Us Setting
            _buildSettingsTile(
              icon: Icons.info,
              title: 'من نحن',
              subtitle: 'معلومات عن Velvete Store',
              onTap: () {
                showToast(
                  title: "Velvete Store",
                  description: "متجرك المفضل للتسوق الإلكتروني",
                  style: ToastNotificationStyleType.info,
                );
              },
            ),
            
            SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: const Color(0xFFB76E79), // Main brand color
            ),
      ),
    );
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: EdgeInsets.symmetric(vertical: 4),
      elevation: 2,
      child: ListTile(
        leading: Container(
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: const Color(0xFFB76E79).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: const Color(0xFFB76E79),
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        subtitle: Text(
          subtitle,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
        ),
        trailing: Icon(
          Icons.chevron_left,
          color: Colors.grey[400],
        ),
        onTap: onTap,
      ),
    );
  }

  Widget _buildToggleTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Card(
      margin: EdgeInsets.symmetric(vertical: 4),
      elevation: 2,
      child: ListTile(
        leading: Container(
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: const Color(0xFFB76E79).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: const Color(0xFFB76E79),
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        subtitle: Text(
          subtitle,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
        ),
        trailing: Switch(
          value: value,
          onChanged: onChanged,
          activeColor: const Color(0xFFB76E79), // Main brand color
          activeTrackColor: const Color(0xFFB76E79).withValues(alpha: 0.3),
          inactiveThumbColor: Colors.grey[400],
          inactiveTrackColor: const Color(0xFF3F3F56).withValues(alpha: 0.3),
        ),
      ),
    );
  }
}
