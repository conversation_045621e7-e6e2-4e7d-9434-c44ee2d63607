import 'package:flutter/material.dart';
import '/bootstrap/extensions.dart';
import '/resources/widgets/buttons/abstract/app_button.dart';
import '/resources/themes/styles/design_constants.dart';

class PrimaryButton extends AppButton {
  final Color? color;

  const PrimaryButton({
    super.key,
    required super.text,
    super.onPressed,
    this.color,
    super.width,
    super.height,
  });

  @override
  Widget build(BuildContext context) {
    return _AnimatedPrimaryButton(
      onPressed: onPressed,
      color: color ?? context.color.buttonBackground,
      child: buildButtonChild(
        context,
        textColor: context.color.buttonContent,
        backgroundColor: Colors.transparent,
      ),
    );
  }
}

/// Enhanced Primary Button with sophisticated micro-interactions
class _AnimatedPrimaryButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final Color color;
  final Widget child;

  const _AnimatedPrimaryButton({
    required this.onPressed,
    required this.color,
    required this.child,
  });

  @override
  State<_AnimatedPrimaryButton> createState() => _AnimatedPrimaryButtonState();
}

class _AnimatedPrimaryButtonState extends State<_AnimatedPrimaryButton>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _colorController;
  late AnimationController _shadowController;

  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;
  late Animation<double> _shadowAnimation;

  @override
  void initState() {
    super.initState();

    // Scale animation for press feedback
    _scaleController = AnimationController(
      duration: DesignConstants.fastAnimation,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: DesignConstants.elegantCurve,
    ));

    // Color animation for press feedback
    _colorController = AnimationController(
      duration: DesignConstants.fastAnimation,
      vsync: this,
    );
    _colorAnimation = ColorTween(
      begin: widget.color,
      end: widget.color.withValues(alpha: 0.8),
    ).animate(CurvedAnimation(
      parent: _colorController,
      curve: DesignConstants.smoothCurve,
    ));

    // Shadow animation for depth feedback
    _shadowController = AnimationController(
      duration: DesignConstants.normalAnimation,
      vsync: this,
    );
    _shadowAnimation = Tween<double>(
      begin: 1.0,
      end: 0.3,
    ).animate(CurvedAnimation(
      parent: _shadowController,
      curve: DesignConstants.elegantCurve,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _colorController.dispose();
    _shadowController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.onPressed != null) {
      _scaleController.forward();
      _colorController.forward();
      _shadowController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    _resetAnimations();
  }

  void _handleTapCancel() {
    _resetAnimations();
  }

  void _resetAnimations() {
    _scaleController.reverse();
    _colorController.reverse();
    _shadowController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      onTap: widget.onPressed,
      child: AnimatedBuilder(
        animation: Listenable.merge([
          _scaleAnimation,
          _colorAnimation,
          _shadowAnimation,
        ]),
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              decoration: DesignConstants.buttonDecoration(
                backgroundColor: _colorAnimation.value ?? widget.color,
              ).copyWith(
                boxShadow: DesignConstants.buttonShadow.map((shadow) {
                  return shadow.copyWith(
                    blurRadius: shadow.blurRadius * _shadowAnimation.value,
                    offset: shadow.offset * _shadowAnimation.value,
                  );
                }).toList(),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(DesignConstants.buttonRadius),
                  onTap: () {}, // Handled by GestureDetector
                  child: widget.child,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
