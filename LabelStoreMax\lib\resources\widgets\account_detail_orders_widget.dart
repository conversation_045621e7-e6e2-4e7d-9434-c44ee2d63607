//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/resources/pages/account_order_detail_page.dart';
import '/app/services/woocommerce_service.dart';
import '/app/services/auth_service.dart';
import '/bootstrap/helpers.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';

class AccountDetailOrdersWidget extends StatefulWidget {
  const AccountDetailOrdersWidget({super.key});

  @override
  createState() => _AccountDetailOrdersWidgetState();
}

class _AccountDetailOrdersWidgetState
    extends NyState<AccountDetailOrdersWidget> {
  @override
  Widget view(BuildContext context) {
    return NyPullToRefresh(
        child: (context, order) {
          order as WooOrder;
          return Card(
            child: ListTile(
              contentPadding: EdgeInsets.only(
                top: 5,
                bottom: 5,
                left: 8,
                right: 6,
              ),
              title: Container(
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: Color(0xFFFCFCFC),
                      width: 1,
                    ),
                  ),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Text(
                      "#${order.id.toString()}",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: match(
                            order.status,
                            () => {
                                  "completed": Colors.green,
                                  "processing": Colors.orange,
                                  "cancelled": Colors.red,
                                  "refunded": Colors.red,
                                  "failed": Colors.red,
                                  "on-hold": Colors.orange,
                                  "pending": Colors.orange,
                                },
                            defaultValue: Colors.grey),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        order.status!.name.capitalize(),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ),
              subtitle: Padding(
                padding: const EdgeInsets.only(top: 10),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Text(
                          formatStringCurrency(total: order.total?.toString()),
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium!
                              .copyWith(fontWeight: FontWeight.w600),
                          textAlign: TextAlign.left,
                        ),
                        Text(
                          "${order.lineItems!.length} ${trans("items")}",
                          style: Theme.of(context)
                              .textTheme
                              .bodyLarge!
                              .copyWith(fontWeight: FontWeight.w600),
                          textAlign: TextAlign.left,
                        ),
                      ],
                    ),
                    Text(
                      "${dateFormatted(
                        date: order.dateCreated!.toIso8601String(),
                        formatType: formatForDateTime(FormatType.date),
                      )}\n${dateFormatted(
                        date: order.dateCreated!.toIso8601String(),
                        formatType: formatForDateTime(FormatType.time),
                      )}",
                      textAlign: TextAlign.right,
                      style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                            fontWeight: FontWeight.w400,
                          ),
                    ),
                  ],
                ),
              ),
              trailing: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Icon(Icons.chevron_right),
                ],
              ),
              onTap: () => _viewOrderDetail(order.id),
            ),
          );
        },
        data: (page) async {
          // Helper function to safely parse the status string into the required enum.
          WooOrderStatus _parseStatus(String? status) {
            switch (status?.toLowerCase()) {
              case 'processing':
                return WooOrderStatus.processing;
              case 'completed':
                return WooOrderStatus.completed;
              case 'on-hold':
                return WooOrderStatus.onHold;
              case 'pending':
                return WooOrderStatus.pending;
              case 'cancelled':
                return WooOrderStatus.cancelled;
              case 'refunded':
                return WooOrderStatus.refunded;
              case 'failed':
                return WooOrderStatus.failed;
              default:
                // Provide a safe default if the status is unknown or null.
                return WooOrderStatus.processing;
            }
          }

          print('🌐 ===== PROFILE ORDERS: FETCHING USER ORDERS =====');

          // Get the reliable WooCommerce customer ID from the active session.
          int? customerId;
          final userSession = await AuthService().getCurrentUser();
          if (userSession != null && userSession.id != null) {
              customerId = int.tryParse(userSession.id!);
          }

          if (customerId == null) {
            print("❌ Cannot fetch orders: User is not logged in or customer ID is missing.");
            return <WooOrder>[];
          }

          print("Fetching orders for customer ID: $customerId");
          try {
            // Fetch the raw order data as a list of maps, bypassing the broken FromJson.
            final response = await WooCommerceService().wooCommerce.dio.get('/orders', queryParameters: {
              'customer': customerId.toString(),
              'per_page': '100', // Fetch up to 100 orders
            });

            final List<dynamic> rawOrders = response.data as List<dynamic>;

            // Manually parse the raw data into WooOrder objects.
            List<WooOrder> correctlyParsedOrders = rawOrders.map((json) {
              // Create the WooOrder object manually, handling the status field correctly.
              return WooOrder(
                id: json['id'],
                number: json['number'],
                orderKey: json['order_key'],
                status: _parseStatus(json['status']), // Use our safe parsing function.
                dateCreated: DateTime.tryParse(json['date_created_gmt'] ?? ''),
                total: double.tryParse(json['total']?.toString() ?? '0'),
                lineItems: (json['line_items'] as List).map((item) {
                  // Manually parse each line item, ensuring all fields are correctly typed.
                  return WooLineItem(
                    id: item['id'],
                    name: item['name'],
                    productId: item['product_id'],
                    variationId: item['variation_id'],
                    quantity: item['quantity'],
                    // CRITICAL FIX: Ensure all price fields are safely converted to double for the WooLineItem constructor.
                    subtotal: double.tryParse(item['subtotal']?.toString() ?? '0.00'),
                    total: double.tryParse(item['total']?.toString() ?? '0.00'),
                    sku: item['sku'] ?? '',
                    price: (item['price'] as num?)?.toDouble() ?? 0.0, // Convert to double if it's a number, default to 0.0
                  );
                }).toList(),
                // Add other fields as needed for display.
              );
            }).toList();

            print("✅ Found and correctly parsed ${correctlyParsedOrders.length} orders for customer.");
            return correctlyParsedOrders;

          } catch (e, stackTrace) {
            print('🌐 ===== PROFILE ORDERS: CRITICAL ERROR =====');
            print('❌ Exception during manual orders fetch: $e');
            print('📋 Stack Trace: $stackTrace');
            return <WooOrder>[];
          }
        },
        empty: Container(
          margin: EdgeInsets.all(24),
          child: Center(
            child: Container(
              padding: EdgeInsets.all(40),
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? const Color(0xFF2A2A2A)
                    : const Color(0xFFF8F6FF),
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: Theme.of(context).brightness == Brightness.dark ? 0.3 : 0.08),
                    blurRadius: 20,
                    offset: Offset(0, 8),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Sad Receipt Illustration
                  Container(
                    width: 140,
                    height: 140,
                    decoration: BoxDecoration(
                      color: const Color(0xFFB76E79).withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        Icon(
                          Icons.receipt_long_outlined,
                          size: 70,
                          color: const Color(0xFFB76E79).withValues(alpha: 0.7),
                        ),
                        // Sad face overlay
                        Positioned(
                          bottom: 35,
                          child: Container(
                            width: 30,
                            height: 15,
                            decoration: BoxDecoration(
                              color: const Color(0xFFB76E79).withValues(alpha: 0.8),
                              borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(15),
                                bottomRight: Radius.circular(15),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 32),

                  // Primary Bold Message
                  Text(
                    'لا توجد طلبات',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : const Color(0xFF2E3A59),
                        ),
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: 16),

                  // Secondary Supportive Message
                  Text(
                    'لم تقم بأي طلبات حتى الآن\nابدأ التسوق واكتشف منتجاتنا المميزة',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.grey[400]
                              : Colors.grey[600],
                          height: 1.6,
                          fontSize: 16,
                        ),
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: 40),

                  // Go Shopping Button
                  SizedBox(
                    width: double.infinity,
                    height: 56,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF6366F1), // Blue-purple color
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        elevation: 0,
                        shadowColor: Colors.transparent,
                      ),
                      child: Text(
                        'ابدأ التسوق',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        loadingStyle: LoadingStyle.skeletonizer(
          child: SizedBox(
            height: 200,
            width: double.infinity,
            child: ListView(
              children: [
                Card(
                  child: ListTile(
                    contentPadding: EdgeInsets.only(
                      top: 5,
                      bottom: 5,
                      left: 8,
                      right: 6,
                    ),
                    title: Container(
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: Color(0xFFFCFCFC),
                            width: 1,
                          ),
                        ),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: <Widget>[
                          Text(
                            "Some Text",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            "Some Text",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    subtitle: Padding(
                      padding: const EdgeInsets.only(top: 10),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: <Widget>[
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: <Widget>[
                              Text(
                                formatStringCurrency(total: "Some Text"),
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium!
                                    .copyWith(fontWeight: FontWeight.w600),
                                textAlign: TextAlign.left,
                              ),
                              Text(
                                "Some Text",
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyLarge!
                                    .copyWith(fontWeight: FontWeight.w600),
                                textAlign: TextAlign.left,
                              ),
                            ],
                          ),
                          Text(
                            "Some Text",
                            textAlign: TextAlign.right,
                            style:
                                Theme.of(context).textTheme.bodyLarge!.copyWith(
                                      fontWeight: FontWeight.w400,
                                    ),
                          ),
                        ],
                      ),
                    ),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Icon(Icons.chevron_right),
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ));
  }

  _viewOrderDetail(int? orderId) {
    routeTo(AccountOrderDetailPage.path, data: orderId);
  }
}
