//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

class MenuLink {
  final String title;
  final String? url;
  final String? routeName;
  final String? icon;

  MenuLink({
    required this.title,
    this.url,
    this.routeName,
    this.icon,
  });

  factory MenuLink.fromJson(Map<String, dynamic> json) {
    return MenuLink(
      title: json['title'] ?? '',
      url: json['url'],
      routeName: json['route_name'],
      icon: json['icon'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'url': url,
      'route_name': routeName,
      'icon': icon,
    };
  }
}

class AppConfig {
  String? appName;
  String? appLogo;
  String? theme;
  String? themeFont;
  Map<String, dynamic>? themeColors;
  Map<String, dynamic>? currencyMeta;
  bool? firebaseFcmIsEnabled;
  Map<String, dynamic>? firebaseOptionsAndroid;
  Map<String, dynamic>? firebaseOptionsIos;
  int? wpLoginEnabled;
  String? wpLoginBaseUrl;
  String? wpLoginForgotPasswordUrl;
  String? wpLoginWpApiPath;
  String? locale;
  String? paypalLocale;
  String? stripeAccount;
  String? stripeCountryCode;
  bool? stripeLiveMode;
  bool? stripeEnabled;
  String? razorpayKeyId;
  bool? razorpayLiveMode;
  String? paypalEmail;
  bool? paypalLiveMode;
  bool? paypalEnabled;
  bool? codEnabled;
  bool? showRelatedProducts;
  bool? showUpsellProducts;
  bool? wishlistEnabled;
  bool? showProductReviews;
  bool? disableShipping;
  String? bannerHeight;
  String? productPricesIncludeTax;
  String? wpLoginUserRole;
  String? appStatus;

  // Menu links configuration
  List<MenuLink>? menuLinks;

  // Terms and privacy links
  String? termsUrl;
  String? privacyUrl;

  // Banner images
  List<String>? bannerImages;
  
  AppConfig({
    this.appName,
    this.appLogo,
    this.theme,
    this.themeFont,
    this.themeColors,
    this.currencyMeta,
    this.firebaseFcmIsEnabled,
    this.firebaseOptionsAndroid,
    this.firebaseOptionsIos,
    this.wpLoginEnabled,
    this.wpLoginBaseUrl,
    this.wpLoginForgotPasswordUrl,
    this.wpLoginWpApiPath,
    this.locale,
    this.paypalLocale,
    this.stripeAccount,
    this.stripeCountryCode,
    this.stripeLiveMode,
    this.stripeEnabled,
    this.razorpayKeyId,
    this.razorpayLiveMode,
    this.paypalEmail,
    this.paypalLiveMode,
    this.paypalEnabled,
    this.codEnabled,
    this.showRelatedProducts,
    this.showUpsellProducts,
    this.wishlistEnabled,
    this.showProductReviews,
    this.disableShipping,
    this.bannerHeight,
    this.productPricesIncludeTax,
    this.wpLoginUserRole,
    this.appStatus,
    this.menuLinks,
    this.termsUrl,
    this.privacyUrl,
    this.bannerImages,
  });
  
  // Factory constructor to create a default configuration
  factory AppConfig.defaultConfig() {
    return AppConfig(
      appName: "Velvete",
      appLogo: null, // Logo URL can be set later
      theme: "mello",
      themeFont: "Poppins",
      themeColors: {
        'light': {
          'background': '0xFFFFFFFF', // Primary Background: #FFFFFF
          'primary_text': '0xFF2E3A59', // Deep Navy for primary text: #2E3A59
          'button_background': '0xFFB76E79', // Main brand color: #B76E79
          'button_text': '0xFFFFFFFF', // White text on buttons
          'app_bar_background': '0xFFFFFFFF', // White app bar background
          'app_bar_text': '0xFF2E3A59', // Deep Navy for app bar text: #2E3A59
        },
        'dark': {
          'background': '0xFF212121', // Dark background
          'primary_text': '0xFFE1E1E1', // Light text for dark mode
          'button_background': '0xFFB76E79', // Main brand color: #B76E79
          'button_text': '0xFFFFFFFF', // White text on buttons
          'app_bar_background': '0xFF2C2C2C', // Dark app bar background
          'app_bar_text': '0xFFE1E1E1', // Light text on dark app bar
        }
      },
      currencyMeta: {
        'symbol': 'LYD',
        'symbolNative': 'د.ل',
        'decimalDigits': 2,
        'rounding': 0,
        'code': 'LYD',
        'namePlural': 'Libyan dinars'
      },
      firebaseFcmIsEnabled: true,
      firebaseOptionsAndroid: {
        'apiKey': 'AIzaSyBHR8q_o0GQeJqZdZbbPRBN7lsVIcIler0',
        'appId': '1:655195009649:android:e9b87d7213997fa76121d0',
        'messagingSenderId': '655195009649',
        'projectId': 'velvete-store-01',
        'storageBucket': 'velvete-store-01.firebasestorage.app',
      },
      firebaseOptionsIos: null, // Firebase config optional
      wpLoginEnabled: 1, // Enable WordPress login
      wpLoginBaseUrl: 'https://velvete.ly',
      wpLoginWpApiPath: '/wp-json/wp/v2',
      stripeEnabled: false,
      paypalEnabled: false,
      codEnabled: true,
      showRelatedProducts: true,
      showUpsellProducts: true,
      wishlistEnabled: true,
      showProductReviews: true,
      disableShipping: false,
      productPricesIncludeTax: "no",
      appStatus: "active",
      menuLinks: [
        MenuLink(
          title: "About Us",
          url: "https://velvete.ly/about-us",
          icon: "info",
        ),
        MenuLink(
          title: "Contact Us",
          url: "https://velvete.ly/contact",
          icon: "contact_support",
        ),
        MenuLink(
          title: "Size Guide",
          url: "https://velvete.ly/sizes",
          icon: "straighten",
        ),
        MenuLink(
          title: "Delivery Info",
          url: "https://velvete.ly/delivery",
          icon: "local_shipping",
        ),
      ],
      termsUrl: "https://velvete.ly/terms-conditions",
      privacyUrl: "https://velvete.ly/privacy-policy",
      bannerImages: [
        "https://velvete.ly/wp-content/uploads/2024/banner1.jpg",
        "https://velvete.ly/wp-content/uploads/2024/banner2.jpg",
        "https://velvete.ly/wp-content/uploads/2024/banner3.jpg",
      ],
    );
  }
}
