//  Velvete Store
//
//  Created by Augment Agent.
//  2025, Velvete Store. All rights reserved.
//

import 'package:flutter/material.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/resources/pages/auth_options_page.dart';

class OnboardingPage extends NyStatefulWidget {
  static RouteView path = ("/onboarding", (_) => OnboardingPage());

  OnboardingPage({super.key}) : super(child: () => _OnboardingPageState());
}

class _OnboardingPageState extends NyPage<OnboardingPage> {
  PageController _pageController = PageController();
  int _currentPage = 0;

  final List<OnboardingStep> _steps = [
    OnboardingStep(
      image: 'public/images/onboarding/step1.png',
      title: 'مرحبا بكِ عزيزتي!',
      description: 'متجر فيلفيت متشوق لخدمتكِ',
      isAsset: true,
    ),
    OnboardingStep(
      image: 'public/images/onboarding/step2.png',
      title: 'اشري اونلاين وانتي في مكانك!',
      description: 'شوفي احدث التصاميم والعروض. خليك ديما اول من يعرف واحلى من يلبس!',
      isAsset: true,
    ),
    OnboardingStep(
      image: 'public/images/onboarding/step1.png', // Using step1 for now until step3 is available
      title: 'هيا نبدأ !',
      description: 'ياسر مقدمات، خلينا نشوفوا شن فيه',
      isAsset: true,
    ),
  ];

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Skip button
            Padding(
              padding: EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: _completeOnboarding,
                    child: Text(
                      'تخطي',
                      style: TextStyle(
                        color: Color(0xFFB76E79),
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // Page view
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemCount: _steps.length,
                itemBuilder: (context, index) {
                  return _buildOnboardingStep(_steps[index]);
                },
              ),
            ),
            
            // Page indicators
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                _steps.length,
                (index) => Container(
                  margin: EdgeInsets.symmetric(horizontal: 4),
                  width: _currentPage == index ? 24 : 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: _currentPage == index
                        ? Color(0xFFB76E79)
                        : Color(0xFFB76E79).withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ),
            
            SizedBox(height: 20),
            
            // Navigation buttons
            Padding(
              padding: EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Previous button
                  _currentPage > 0
                      ? TextButton(
                          onPressed: _previousPage,
                          child: Text(
                            'السابق',
                            style: TextStyle(
                              color: Color(0xFFB76E79),
                              fontSize: 16,
                            ),
                          ),
                        )
                      : SizedBox(width: 60),
                  
                  // Next/Start button
                  ElevatedButton(
                    onPressed: _currentPage == _steps.length - 1 
                        ? _completeOnboarding 
                        : _nextPage,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(0xFFB76E79),
                      padding: EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                    ),
                    child: Text(
                      _currentPage == _steps.length - 1 ? 'هيا نبدأ!' : 'التالي',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildOnboardingStep(OnboardingStep step) {
    return Padding(
      padding: EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Image
          step.isAsset
              ? Image.asset(
                  step.image,
                  height: 300,
                  width: 300,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    print('⚠️ Failed to load onboarding image: ${step.image}');
                    return _buildImagePlaceholder();
                  },
                )
              : Image.network(
                  step.image,
                  height: 300,
                  width: 300,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    print('⚠️ Failed to load onboarding image: ${step.image}');
                    return _buildImagePlaceholder();
                  },
                ),
          
          SizedBox(height: 40),
          
          // Title
          Text(
            step.title,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFFB76E79),
            ),
          ),
          
          SizedBox(height: 16),
          
          // Description
          Text(
            step.description,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  void _nextPage() {
    _pageController.nextPage(
      duration: Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _previousPage() {
    _pageController.previousPage(
      duration: Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _completeOnboarding() async {
    // Mark onboarding as seen
    await NyStorage.save('has_seen_onboarding', true);

    // Navigate to auth options
    routeTo(AuthOptionsPage.path, navigationType: NavigationType.pushReplace);
  }

  Widget _buildImagePlaceholder() {
    return Container(
      height: 300,
      width: 300,
      decoration: BoxDecoration(
        color: Color(0xFFB76E79).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.store,
            size: 80,
            color: Color(0xFFB76E79),
          ),
          SizedBox(height: 16),
          Text(
            'Velvete Store',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFFB76E79),
            ),
          ),
        ],
      ),
    );
  }
}

class OnboardingStep {
  final String image;
  final String title;
  final String description;
  final bool isAsset;

  OnboardingStep({
    required this.image,
    required this.title,
    required this.description,
    this.isAsset = false,
  });
}
