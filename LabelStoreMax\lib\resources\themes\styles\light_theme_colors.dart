import 'package:flutter/material.dart';
import '/resources/themes/styles/color_styles.dart';

/* Light Theme Colors - Velvete Brand Colors
|-------------------------------------------------------------------------- */

class LightThemeColors implements ColorStyles {
  // === LUXURIOUS LIGHT THEME PALETTE ===

  // Primary Background & Surfaces
  @override
  Color get background => const Color(0xFFFFFFFF); // Pure White: #FFFFFF
  @override
  Color get backgroundContainer => const Color(0xFFFFF9F9); // Soft Blush White: #FFF9F9
  @override
  Color get surfaceBackground => const Color(0xFFFFF9F9); // Soft Blush White for cards: #FFF9F9

  // Text Colors - Sophisticated Hierarchy
  @override
  Color get content => const Color(0xFF2E2E2E); // Rich Charcoal: #2E2E2E
  @override
  Color get surfaceContent => const Color(0xFF2E2E2E); // Rich Charcoal for surface text: #2E2E2E
  @override
  Color get inputPrimaryContent => const Color(0xFF2E2E2E); // Rich Charcoal for input text: #2E2E2E

  // Brand Accent Colors - Elegant Mauve Palette
  @override
  Color get primaryAccent => const Color(0xFFA45C7B); // Deeper Mauve: #A45C7B (primary accent)

  // App Bar - Clean & Sophisticated
  @override
  Color get appBarBackground => const Color(0xFFFFFFFF); // Pure White app bar: #FFFFFF
  @override
  Color get appBarPrimaryContent => const Color(0xFF2E2E2E); // Rich Charcoal for app bar text: #2E2E2E

  // Buttons - Impactful & Clear
  @override
  Color get buttonBackground => const Color(0xFF9A0000); // Oxblood Red for primary CTAs: #9A0000
  @override
  Color get buttonContent => const Color(0xFFFFFFFF); // Pure White text on primary buttons
  @override
  Color get buttonSecondaryBackground => Colors.transparent; // Transparent for outlined buttons
  @override
  Color get buttonSecondaryContent => const Color(0xFFA45C7B); // Deeper Mauve for secondary button text: #A45C7B

  // Bottom Navigation - Elegant & Clear
  @override
  Color get bottomTabBarBackground => const Color(0xFFFFFFFF); // Pure White background for bottom nav

  // bottom tab bar - icons
  @override
  Color get bottomTabBarIconSelected => const Color(0xFFA45C7B); // Deeper Mauve for selected icon: #A45C7B
  @override
  Color get bottomTabBarIconUnselected => const Color(0xFF2E2E2E); // Rich Charcoal for unselected icons: #2E2E2E

  // bottom tab bar - label
  @override
  Color get bottomTabBarLabelUnselected => const Color(0xFF2E2E2E); // Rich Charcoal for unselected labels: #2E2E2E
  @override
  Color get bottomTabBarLabelSelected => const Color(0xFFA45C7B); // Deeper Mauve for selected label: #A45C7B

  // Additional Accent Colors for UI Elements
  Color get coolLightGray => const Color(0xFFD9D9D9); // Cool Light Gray for borders: #D9D9D9
  Color get softBlushAccent => const Color(0xFFF6DDE2); // Soft Blush for accents: #F6DDE2
  Color get warmBlushAccent => const Color(0xFFF8CED8); // Warm Blush for highlights: #F8CED8
  Color get rosyBlushAccent => const Color(0xFFFFC4CE); // Rosy Blush for special elements: #FFC4CE
  Color get dustyRoseAccent => const Color(0xFFE78FA2); // Dusty Rose for emphasis: #E78FA2
  Color get goldenAccent => const Color(0xFFFAD440); // Golden Yellow for banners: #FAD440
}
