import 'package:flutter/material.dart';
import '/resources/themes/styles/color_styles.dart';

/* Dark Theme Colors - Velvete Brand Colors (Dark Mode)
|-------------------------------------------------------------------------- */

class DarkThemeColors implements ColorStyles {
  // === LUXURIOUS DARK THEME PALETTE ===

  // Primary Background & Surfaces - Rich & Deep
  @override
  Color get background => const Color(0xFF1A1A1A); // Deep Charcoal: #1A1A1A
  @override
  Color get backgroundContainer => const Color(0xFF2A1F1F); // Warm Dark Brown: #2A1F1F
  @override
  Color get surfaceBackground => const Color(0xFF2A1F1F); // Warm Dark Brown for cards: #2A1F1F

  // Text Colors - Elegant Light Hierarchy
  @override
  Color get content => const Color(0xFFEDE4D8); // Warm Cream: #EDE4D8
  @override
  Color get surfaceContent => const Color(0xFFEDE4D8); // Warm Cream for surface text: #EDE4D8
  @override
  Color get inputPrimaryContent => const Color(0xFFEDE4D8); // Warm Cream for input text: #EDE4D8

  // Brand Accent Colors - Sophisticated Dark Mauve
  @override
  Color get primaryAccent => const Color(0xFF6A3A5A); // Dark Mauve: #6A3A5A (primary accent)

  // App Bar - Sophisticated Dark
  @override
  Color get appBarBackground => const Color(0xFF1A1A1A); // Deep Charcoal app bar: #1A1A1A
  @override
  Color get appBarPrimaryContent => const Color(0xFFEDE4D8); // Warm Cream for app bar text: #EDE4D8

  // Buttons - Impactful Dark Theme
  @override
  Color get buttonBackground => const Color(0xFFA80000); // Dark Oxblood Red for primary CTAs: #A80000
  @override
  Color get buttonContent => const Color(0xFFFFFFFF); // Pure White text on primary buttons

  @override
  Color get buttonSecondaryBackground => Colors.transparent; // Transparent for outlined buttons
  @override
  Color get buttonSecondaryContent => const Color(0xFF6A3A5A); // Dark Mauve for secondary button text: #6A3A5A

  // Bottom Navigation - Elegant Dark
  @override
  Color get bottomTabBarBackground => const Color(0xFF1A1A1A); // Deep Charcoal background for bottom nav

  // bottom tab bar - icons
  @override
  Color get bottomTabBarIconSelected => const Color(0xFF6A3A5A); // Dark Mauve for selected icon: #6A3A5A
  @override
  Color get bottomTabBarIconUnselected => const Color(0xFFEDE4D8); // Warm Cream for unselected icons

  // bottom tab bar - label
  @override
  Color get bottomTabBarLabelUnselected => const Color(0xFFEDE4D8); // Warm Cream for unselected labels
  @override
  Color get bottomTabBarLabelSelected => const Color(0xFF6A3A5A); // Dark Mauve for selected label: #6A3A5A

  // Additional Dark Theme Accent Colors
  Color get darkCoolGray => const Color(0xFF4A4A4A); // Dark Cool Gray for borders: #4A4A4A
  Color get darkBlushAccent => const Color(0xFF3D2A2F); // Dark Blush for accents: #3D2A2F
  Color get darkWarmAccent => const Color(0xFF3F2D32); // Dark Warm for highlights: #3F2D32
  Color get darkRosyAccent => const Color(0xFF4A2D35); // Dark Rosy for special elements: #4A2D35
  Color get darkDustyAccent => const Color(0xFF3A2A30); // Dark Dusty for emphasis: #3A2A30
  Color get darkGoldenAccent => const Color(0xFF5A4A20); // Dark Golden for banners: #5A4A20
}
