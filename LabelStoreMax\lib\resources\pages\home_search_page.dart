//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/resources/pages/browse_search_page.dart';
import '/resources/widgets/safearea_widget.dart';
import 'package:nylo_framework/nylo_framework.dart';

class HomeSearchPage extends NyStatefulWidget {
  static RouteView path = ("/home-search", (_) => HomeSearchPage());

  HomeSearchPage({super.key}) : super(child: () => _HomeSearchPageState());
}

class _HomeSearchPageState extends NyPage<HomeSearchPage> {
  final TextEditingController _txtSearchController = TextEditingController();

  _actionSearch() {
    if (_txtSearchController.text.trim().isNotEmpty) {
      routeTo(BrowseSearchPage.path, data: _txtSearchController.text.trim());
    }
  }

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'البحث',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).appBarTheme.titleTextStyle?.color,
          ),
        ),
        centerTitle: true,
        automaticallyImplyLeading: false,
      ),
      body: SafeAreaWidget(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              // Prominent Search Bar
              Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 12,
                      offset: Offset(0, 4),
                    ),
                  ],
                ),
                child: TextField(
                  controller: _txtSearchController,
                  textAlign: TextAlign.right,
                  autofocus: true,
                  onSubmitted: (value) => _actionSearch(),
                  decoration: InputDecoration(
                    hintText: 'ابحث عن المنتجات...',
                    hintStyle: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 16,
                    ),
                    prefixIcon: IconButton(
                      icon: Icon(
                        Icons.search,
                        color: const Color(0xFFB76E79),
                        size: 28,
                      ),
                      onPressed: _actionSearch,
                    ),
                    suffixIcon: _txtSearchController.text.isNotEmpty
                        ? IconButton(
                            icon: Icon(
                              Icons.clear,
                              color: Colors.grey[400],
                            ),
                            onPressed: () {
                              setState(() {
                                _txtSearchController.clear();
                              });
                            },
                          )
                        : null,
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 20,
                    ),
                  ),
                  style: TextStyle(
                    fontSize: 16,
                    color: Theme.of(context).textTheme.bodyLarge?.color,
                  ),
                ),
              ),

              SizedBox(height: 32),

              // Search Button
              SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: _actionSearch,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFB76E79),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    elevation: 4,
                  ),
                  child: Text(
                    'بحث',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

              SizedBox(height: 48),

              // Empty State Illustration
              Expanded(
                child: _buildEmptyState(context),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    bool isDark = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Search Illustration
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: const Color(0xFFB76E79).withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.search,
              size: 60,
              color: const Color(0xFFB76E79),
            ),
          ),

          SizedBox(height: 24),

          // Main Message
          Text(
            'ابحث عن منتجاتك المفضلة',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: isDark ? Colors.white : const Color(0xFF2E3A59),
                ),
            textAlign: TextAlign.center,
          ),

          SizedBox(height: 12),

          // Subtitle
          Text(
            'اكتشف آلاف المنتجات المتنوعة\nواعثر على ما تبحث عنه بسهولة',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.grey[600],
                  height: 1.5,
                ),
            textAlign: TextAlign.center,
          ),

          SizedBox(height: 32),

          // Popular Search Suggestions
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildSearchChip('ملابس'),
              _buildSearchChip('إكسسوارات'),
              _buildSearchChip('أحذية'),
              _buildSearchChip('حقائب'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSearchChip(String label) {
    return GestureDetector(
      onTap: () {
        _txtSearchController.text = label;
        _actionSearch();
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: const Color(0xFFB76E79).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: const Color(0xFFB76E79).withValues(alpha: 0.3),
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: const Color(0xFFB76E79),
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
