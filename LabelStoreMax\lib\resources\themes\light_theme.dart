import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '/bootstrap/app_helper.dart';
import '/config/font.dart';
import '/resources/themes/styles/color_styles.dart';
import '/resources/themes/styles/design_constants.dart';
import '/resources/themes/text_theme/default_text_theme.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:nylo_framework/nylo_framework.dart';

/* Light Theme
|--------------------------------------------------------------------------
| Theme Config - config/theme.dart
|-------------------------------------------------------------------------- */

ThemeData lightTheme(ColorStyles lightColors) {
  // Luxurious Typography - Arabic & Latin Support
  TextStyle arabicFont;
  TextStyle latinFont;

  try {
    // Arabic Font - Clean, elegant sans-serif for Arabic text
    arabicFont = GoogleFonts.getFont("Cairo"); // Clean Arabic sans-serif
    // Latin Font - Modern, complementary sans-serif for Latin text
    latinFont = GoogleFonts.getFont("Inter"); // Modern Latin sans-serif
  } on Exception catch (e) {
    if (getEnv('APP_DEBUG') == true) {
      NyLogger.error(e.toString());
    }
    // Fallback fonts
    arabicFont = const TextStyle(fontFamily: 'Cairo');
    latinFont = const TextStyle(fontFamily: 'Inter');
  }

  // Create sophisticated text theme with proper hierarchy
  TextTheme lightTheme = _createLuxuriousTextTheme(lightColors, arabicFont, latinFont);

  return ThemeData(
    useMaterial3: true,
    primaryColor: lightColors.content,
    primaryColorLight: lightColors.primaryAccent,
    focusColor: lightColors.content,
    scaffoldBackgroundColor: lightColors.background,
    hintColor: lightColors.primaryAccent,
    appBarTheme: AppBarTheme(
      surfaceTintColor: Colors.transparent,
      backgroundColor: lightColors.appBarBackground,
      titleTextStyle: lightTheme.titleLarge!
          .copyWith(color: lightColors.appBarPrimaryContent),
      iconTheme: IconThemeData(color: lightColors.appBarPrimaryContent),
      elevation: 0, // Remove default elevation
      shadowColor: Colors.transparent,
      systemOverlayStyle: SystemUiOverlayStyle.dark,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(DesignConstants.cardRadius),
          bottomRight: Radius.circular(DesignConstants.cardRadius),
        ),
      ),
    ),
    buttonTheme: ButtonThemeData(
      buttonColor: lightColors.buttonContent,
      colorScheme: ColorScheme.light(primary: lightColors.buttonBackground),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(foregroundColor: lightColors.content),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: lightColors.buttonBackground,
        foregroundColor: lightColors.buttonContent,
        elevation: 0,
        shadowColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignConstants.buttonRadius),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: DesignConstants.baseHorizontalPadding,
          vertical: DesignConstants.baseVerticalPadding,
        ),
      ),
    ),
    cardTheme: CardThemeData(
      color: lightColors.backgroundContainer,
      elevation: 0,
      shadowColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(DesignConstants.cardRadius),
      ),
      margin: EdgeInsets.all(DesignConstants.compactSpacing),
    ),
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: lightColors.bottomTabBarBackground,
      unselectedIconTheme:
          IconThemeData(color: lightColors.bottomTabBarIconUnselected),
      selectedIconTheme:
          IconThemeData(color: lightColors.bottomTabBarIconSelected),
      unselectedLabelStyle:
          TextStyle(color: lightColors.bottomTabBarLabelUnselected),
      selectedLabelStyle:
          TextStyle(color: lightColors.bottomTabBarLabelSelected),
      selectedItemColor: lightColors.bottomTabBarLabelSelected,
    ),
    textTheme: lightTheme,
    colorScheme: ColorScheme.light(
        surface: lightColors.background, primary: lightColors.content),
  );
}

/*
|--------------------------------------------------------------------------
| Light Text Theme
|--------------------------------------------------------------------------
*/

TextTheme _textTheme(ColorStyles colors) {
  Color primaryContent = colors.content;
  TextTheme textTheme = TextTheme().apply(displayColor: primaryContent);
  return textTheme.copyWith(
    labelLarge: TextStyle(color: primaryContent.withValues(alpha: 0.8)),
    bodyMedium: TextStyle(color: primaryContent.withValues(alpha: 0.8)),
  );
}
