import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '/resources/themes/styles/color_styles.dart';
import '/resources/themes/styles/design_constants.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:nylo_framework/nylo_framework.dart';

/* Light Theme
|--------------------------------------------------------------------------
| Theme Config - config/theme.dart
|-------------------------------------------------------------------------- */

ThemeData lightTheme(ColorStyles lightColors) {
  // Luxurious Typography - Arabic & Latin Support
  TextStyle arabicFont;
  TextStyle latinFont;

  try {
    // Arabic Font - Clean, elegant sans-serif for Arabic text
    arabicFont = GoogleFonts.getFont("Cairo"); // Clean Arabic sans-serif
    // Latin Font - Modern, complementary sans-serif for Latin text
    latinFont = GoogleFonts.getFont("Inter"); // Modern Latin sans-serif
  } on Exception catch (e) {
    if (getEnv('APP_DEBUG') == true) {
      NyLogger.error(e.toString());
    }
    // Fallback fonts
    arabicFont = const TextStyle(fontFamily: 'Cairo');
    latinFont = const TextStyle(fontFamily: 'Inter');
  }

  // Create sophisticated text theme with proper hierarchy
  TextTheme lightTheme = _createLuxuriousTextTheme(lightColors, arabicFont, latinFont);

  return ThemeData(
    useMaterial3: true,
    primaryColor: lightColors.content,
    primaryColorLight: lightColors.primaryAccent,
    focusColor: lightColors.content,
    scaffoldBackgroundColor: lightColors.background,
    hintColor: lightColors.primaryAccent,
    appBarTheme: AppBarTheme(
      surfaceTintColor: Colors.transparent,
      backgroundColor: lightColors.appBarBackground,
      titleTextStyle: lightTheme.titleLarge!
          .copyWith(color: lightColors.appBarPrimaryContent),
      iconTheme: IconThemeData(color: lightColors.appBarPrimaryContent),
      elevation: 0, // Remove default elevation
      shadowColor: Colors.transparent,
      systemOverlayStyle: SystemUiOverlayStyle.dark,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(DesignConstants.cardRadius),
          bottomRight: Radius.circular(DesignConstants.cardRadius),
        ),
      ),
    ),
    buttonTheme: ButtonThemeData(
      buttonColor: lightColors.buttonContent,
      colorScheme: ColorScheme.light(primary: lightColors.buttonBackground),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(foregroundColor: lightColors.content),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color(0xFF9A0000), // Oxblood Red primary CTAs
        foregroundColor: Colors.white,
        elevation: 0,
        shadowColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignConstants.buttonRadius),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: DesignConstants.baseHorizontalPadding,
          vertical: DesignConstants.baseVerticalPadding,
        ),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: const Color(0xFFA45C7B), // Deeper Mauve for secondary buttons
        backgroundColor: const Color(0xFFA45C7B).withValues(alpha: 0.05), // Very subtle fill
        side: BorderSide(color: const Color(0xFFA45C7B), width: 1.5),
        elevation: 0,
        shadowColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignConstants.buttonRadius),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: DesignConstants.baseHorizontalPadding,
          vertical: DesignConstants.baseVerticalPadding,
        ),
      ),
    ),
    cardTheme: CardThemeData(
      color: const Color(0xFFFFF9F9), // Soft Blush White for cards
      elevation: 0,
      shadowColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(DesignConstants.cardRadius),
      ),
      margin: EdgeInsets.all(DesignConstants.compactSpacing),
    ),
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: lightColors.bottomTabBarBackground,
      unselectedIconTheme:
          IconThemeData(color: lightColors.bottomTabBarIconUnselected),
      selectedIconTheme:
          IconThemeData(color: lightColors.bottomTabBarIconSelected),
      unselectedLabelStyle:
          TextStyle(color: lightColors.bottomTabBarLabelUnselected),
      selectedLabelStyle:
          TextStyle(color: lightColors.bottomTabBarLabelSelected),
      selectedItemColor: lightColors.bottomTabBarLabelSelected,
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: const Color(0xFFFFF9F9), // Soft Blush White for input fields
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(DesignConstants.inputRadius),
        borderSide: BorderSide(color: Colors.grey.shade300, width: 1.5),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(DesignConstants.inputRadius),
        borderSide: BorderSide(color: Colors.grey.shade300, width: 1.5),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(DesignConstants.inputRadius),
        borderSide: BorderSide(color: const Color(0xFFA45C7B), width: 2.0), // Deeper Mauve focus
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(DesignConstants.inputRadius),
        borderSide: BorderSide(color: Colors.red.shade400, width: 1.5),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(DesignConstants.inputRadius),
        borderSide: BorderSide(color: Colors.red.shade400, width: 2.0),
      ),
      contentPadding: EdgeInsets.symmetric(
        horizontal: DesignConstants.baseHorizontalPadding,
        vertical: DesignConstants.baseVerticalPadding,
      ),
      hintStyle: TextStyle(
        color: Colors.grey.shade500,
        fontSize: DesignConstants.bodyMedium,
      ),
    ),
    textTheme: lightTheme,
    colorScheme: ColorScheme.light(
        surface: lightColors.background, primary: lightColors.content),
  );
}

/*
|--------------------------------------------------------------------------
| Luxurious Text Theme - Arabic & Latin Typography
|--------------------------------------------------------------------------
*/

/// Creates a sophisticated text theme with proper hierarchy for both Arabic and Latin text
/// Implements elegant typography with proper spacing, weights, and color emphasis
TextTheme _createLuxuriousTextTheme(
  ColorStyles colors,
  TextStyle arabicFont,
  TextStyle latinFont,
) {
  Color primaryContent = colors.content;
  Color secondaryContent = primaryContent.withValues(alpha: 0.8);
  Color tertiaryContent = primaryContent.withValues(alpha: 0.6);

  // Base text style with elegant spacing
  TextStyle baseStyle = TextStyle(
    color: primaryContent,
    letterSpacing: 0.3,
    height: 1.4, // Airy line spacing
  );

  return TextTheme(
    // === DISPLAY STYLES - Hero Text ===
    displayLarge: baseStyle.copyWith(
      fontSize: DesignConstants.displayLarge,
      fontWeight: FontWeight.w700,
      letterSpacing: -0.5,
      height: 1.2,
    ).merge(arabicFont),

    displayMedium: baseStyle.copyWith(
      fontSize: DesignConstants.displayMedium,
      fontWeight: FontWeight.w600,
      letterSpacing: -0.3,
      height: 1.25,
    ).merge(arabicFont),

    displaySmall: baseStyle.copyWith(
      fontSize: DesignConstants.displaySmall,
      fontWeight: FontWeight.w600,
      letterSpacing: -0.2,
      height: 1.3,
    ).merge(arabicFont),

    // === HEADLINE STYLES - Section Headers ===
    headlineLarge: baseStyle.copyWith(
      fontSize: DesignConstants.headlineLarge,
      fontWeight: FontWeight.w600,
      letterSpacing: 0.0,
      height: 1.3,
    ).merge(arabicFont),

    headlineMedium: baseStyle.copyWith(
      fontSize: DesignConstants.headlineMedium,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.1,
      height: 1.35,
    ).merge(arabicFont),

    headlineSmall: baseStyle.copyWith(
      fontSize: DesignConstants.headlineSmall,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.15,
      height: 1.4,
    ).merge(arabicFont),

    // === TITLE STYLES - Card Headers, Product Names ===
    titleLarge: baseStyle.copyWith(
      fontSize: DesignConstants.headlineMedium,
      fontWeight: FontWeight.w600,
      letterSpacing: 0.0,
      height: 1.35,
    ).merge(arabicFont),

    titleMedium: baseStyle.copyWith(
      fontSize: DesignConstants.headlineSmall,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.1,
      height: 1.4,
    ).merge(arabicFont),

    titleSmall: baseStyle.copyWith(
      fontSize: DesignConstants.bodyLarge,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.2,
      height: 1.4,
    ).merge(arabicFont),

    // === BODY STYLES - Main Content ===
    bodyLarge: baseStyle.copyWith(
      fontSize: DesignConstants.bodyLarge,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.3,
      height: 1.5,
      color: secondaryContent,
    ).merge(arabicFont),

    bodyMedium: baseStyle.copyWith(
      fontSize: DesignConstants.bodyMedium,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.25,
      height: 1.45,
      color: secondaryContent,
    ).merge(arabicFont),

    bodySmall: baseStyle.copyWith(
      fontSize: DesignConstants.bodySmall,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.4,
      height: 1.4,
      color: tertiaryContent,
    ).merge(arabicFont),

    // === LABEL STYLES - Buttons, Chips, Captions ===
    labelLarge: baseStyle.copyWith(
      fontSize: DesignConstants.labelLarge,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.5,
      height: 1.3,
    ).merge(latinFont), // Latin for buttons/UI elements

    labelMedium: baseStyle.copyWith(
      fontSize: DesignConstants.labelMedium,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.5,
      height: 1.25,
      color: secondaryContent,
    ).merge(latinFont),

    labelSmall: baseStyle.copyWith(
      fontSize: DesignConstants.labelSmall,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.6,
      height: 1.2,
      color: tertiaryContent,
    ).merge(latinFont),
  );
}


