//  StoreMob
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/resources/widgets/safearea_widget.dart';
import '/app/services/woocommerce_service.dart';

import 'package:nylo_framework/nylo_framework.dart';
// import 'package:woosignal/models/response/woosignal_app.dart'; // Replaced with AppConfig

class NoConnectionPage extends NyStatefulWidget {
  static RouteView path = ("/no-connection", (_) => NoConnectionPage());

  NoConnectionPage({super.key}) : super(child: () => _NoConnectionPageState());
}

class _NoConnectionPageState extends NyPage<NoConnectionPage> {
  _NoConnectionPageState();

  @override
  get init => () {
        if (getEnv('APP_DEBUG') == true) {
          NyLogger.error('WooCommerce site is not connected');
        }
      };

  @override
  Widget view(BuildContext context) {
    bool isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      body: SafeAreaWidget(
        child: Container(
          margin: EdgeInsets.all(24),
          child: Center(
            child: Container(
              padding: EdgeInsets.all(40),
              decoration: BoxDecoration(
                color: isDark ? const Color(0xFF2A2A2A) : const Color(0xFFF8F6FF),
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.08),
                    blurRadius: 20,
                    offset: Offset(0, 8),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Sad Error Illustration
                  Container(
                    width: 140,
                    height: 140,
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 70,
                          color: Colors.red.withValues(alpha: 0.7),
                        ),
                        // Sad face overlay
                        Positioned(
                          bottom: 35,
                          child: Container(
                            width: 30,
                            height: 15,
                            decoration: BoxDecoration(
                              color: Colors.red.withValues(alpha: 0.8),
                              borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(15),
                                bottomRight: Radius.circular(15),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 32),

                  // Primary Bold Message
                  Text(
                    'حدث خطأ ما',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: isDark ? Colors.white : const Color(0xFF2E3A59),
                        ),
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: 16),

                  // Secondary Supportive Message
                  Text(
                    'لا يمكن الاتصال بالخادم\nتحقق من اتصالك بالإنترنت وحاول مرة أخرى',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: isDark ? Colors.grey[400] : Colors.grey[600],
                          height: 1.6,
                          fontSize: 16,
                        ),
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: 40),

                  // Retry Button
                  SizedBox(
                    width: double.infinity,
                    height: 56,
                    child: ElevatedButton(
                      onPressed: _retry,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF6366F1), // Blue-purple color
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        elevation: 0,
                        shadowColor: Colors.transparent,
                      ),
                      child: Text(
                        'إعادة المحاولة',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  _retry() async {
    try {
      print('🔄 Attempting to reconnect to WooCommerce API...');

      // Show loading indicator
      showToast(
        title: trans("Connecting"),
        description: trans("Testing connection to WooCommerce..."),
        icon: Icons.refresh,
        style: ToastNotificationStyleType.info,
      );

      // Test WooCommerce API connection by fetching basic store info
      final wooCommerceService = WooCommerceService();

      // Try to fetch a simple endpoint to test connectivity
      await wooCommerceService.getProducts(page: 1, perPage: 1);

      print('✅ WooCommerce API connection successful');

      // Connection successful - show success message and navigate back
      showToast(
        title: trans("Success"),
        description: trans("Connection restored successfully"),
        icon: Icons.check,
        style: ToastNotificationStyleType.success,
      );

      // Navigate back to the previous screen
      Navigator.of(context).pop();

      // Optionally, navigate to initial route if needed
      // routeToInitial();

    } catch (e) {
      print('❌ WooCommerce API connection failed: $e');

      // Connection failed - show error message
      showToast(
        title: trans("Connection Failed"),
        description: trans("Unable to connect to the store. Please check your internet connection and try again."),
        icon: Icons.error_outline,
        style: ToastNotificationStyleType.danger,
      );
    }
  }
}
