import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import '/bootstrap/helpers.dart';
import '/resources/pages/account_order_detail_page.dart';
import '/resources/widgets/notification_icon_widget.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:wp_json_api/models/wp_user.dart';
import 'package:wp_json_api/wp_json_api.dart';

class NotificationsPage extends NyStatefulWidget {
  static RouteView path = ("/notifications", (_) => NotificationsPage());

  NotificationsPage({super.key})
      : super(child: () => _NotificationsPageState());
}

class _NotificationsPageState extends NyPage<NotificationsPage> {
  WpUser? _wpUser;

  @override
  get init => () async {
        _wpUser = (await WPJsonAPI.wpUser());
      };

  @override
  Widget view(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (bool didPop, result) async {
        await NyNotification.markReadAll();
        updateState(NotificationIcon.state);
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text("الإشعارات"),
          actions: [
            TextButton(
              onPressed: () async {
                await NyNotification.markReadAll();
                showToast(
                    title: "نجح",
                    description: "تم وضع علامة مقروء على جميع الإشعارات",
                    icon: Icons.notifications,
                    style: ToastNotificationStyleType.success);
                setState(() {});
              },
              child: Text(
                "وضع علامة مقروء على الكل",
                style: TextStyle(
                  color: const Color(0xFFB76E79),
                ),
              ),
            ),
          ],
        ),
        body: SafeArea(
            child: Column(
          children: [
            Container(
              width: double.infinity,
              margin: EdgeInsets.only(left: 18, right: 18, bottom: 18),
              padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8)),
              child: Text(
                "عرض آخر 30 يوماً",
                textAlign: TextAlign.center,
                style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey.shade800),
              ),
            ),
            Expanded(
              child: NyFutureBuilder(
                future: NyNotification.allNotifications(),
                child: (context, data) {
                  if (data == null) {
                    return SizedBox.shrink();
                  }

                  // Filter notifications for current user
                  List<NotificationItem> filteredNotifications = data.where((notification) {
                    if (notification.meta != null &&
                        notification.meta!.containsKey('user_id')) {
                      String? userId = notification.meta?['user_id'];
                      if (userId != _wpUser?.id.toString()) {
                        return false;
                      }
                    }
                    return true;
                  }).toList();

                  if (filteredNotifications.isEmpty) {
                    return _buildEmptyNotificationsState(context);
                  }

                  return NyListView.separated(
                    child: (context, item) {
                      item as NotificationItem;
                      String? createdAt = item.createdAt;
                      if (createdAt != null) {
                        DateTime createdAtDate = DateTime.parse(createdAt);
                        createdAt = createdAtDate.toTimeAgoString();
                      }
                      return ListTile(
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 16.0, vertical: 4),
                        title: Text(
                          item.title ?? "",
                          style: TextStyle(
                              fontWeight: item.hasRead == true
                                  ? null
                                  : FontWeight.w800),
                        ),
                        leading: Container(
                          padding: EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(Icons.shopping_bag_outlined),
                        ),
                        subtitle: NyRichText(
                          style: TextStyle(color: Colors.black),
                          children: [
                            Text(
                              item.message ?? "",
                              style: TextStyle(
                                  fontWeight: item.hasRead == true
                                      ? null
                                      : FontWeight.w800),
                            ),
                            if (createdAt != null)
                              Text("\n$createdAt",
                                  style: TextStyle(color: Colors.grey.shade600))
                          ],
                        ),
                        trailing: Text("عرض"),
                        onTap: () {
                          dynamic orderId = item.meta?['order_id'];
                          routeTo(AccountOrderDetailPage.path,
                              data: int.parse(orderId.toString()));
                        },
                      );
                    },
                    data: () async {
                      return filteredNotifications.reversed.toList();
                    },
                    separatorBuilder: (context, index) {
                      return Divider(
                        color: Colors.grey.shade100,
                      );
                    },
                  );
                },
                loadingStyle: LoadingStyle.normal(),
              ),
            ),
          ],
        )),
      ),
    );
  }

  Widget _buildEmptyNotificationsState(BuildContext context) {
    bool isDark = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Padding(
        padding: EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Notification Lottie Animation
            Container(
              width: 150,
              height: 150,
              child: Lottie.asset(
                'public/animations/notification.json',
                width: 150,
                height: 150,
                fit: BoxFit.contain,
                repeat: true,
                errorBuilder: (context, error, stackTrace) {
                  // Fallback to icon if Lottie fails
                  return Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      color: const Color(0xFFB76E79).withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.notifications_outlined,
                      size: 60,
                      color: const Color(0xFFB76E79),
                    ),
                  );
                },
              ),
            ),

            SizedBox(height: 24),

            // Main Message
            Text(
              'لا توجد إشعارات',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.white : const Color(0xFF2E3A59),
                  ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 12),

            // Secondary Message
            Text(
              'ستظهر هنا جميع إشعاراتك المهمة\nحول الطلبات والعروض الجديدة',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: isDark ? Colors.grey[400] : Colors.grey[600],
                    height: 1.5,
                  ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 32),

            // Action Button
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFB76E79),
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 2,
              ),
              child: Text(
                'العودة للتسوق',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
