import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import '/app/models/woocommerce_wrappers/my_product_image.dart';
import '/app/models/woocommerce_wrappers/my_product_variation_attribute.dart';

class MyProductVariation {
  final int id;
  final DateTime? dateCreated;
  final DateTime? dateModified;
  final String? description;
  final String? permalink;
  final String? sku;
  final String? price;
  final String? regularPrice;
  final String? salePrice;
  final DateTime? dateOnSaleFrom;
  final DateTime? dateOnSaleTo;
  final bool? onSale;
  final String? status;
  final bool? purchasable;
  final bool? virtual;
  final bool? downloadable;
  final String? taxStatus;
  final String? taxClass;
  final bool? manageStock;
  final int? stockQuantity;
  final String? stockStatus;
  final String? backorders;
  final bool? backordersAllowed;
  final bool? backordered;
  final String? weight;
  final String? shippingClass;
  final MyProductImage? image;
  final List<MyProductVariationAttribute> attributes;
  final int? menuOrder;
  final List<WooMetaData> metaData;

  MyProductVariation({
    required this.id,
    this.dateCreated,
    this.dateModified,
    this.description,
    this.permalink,
    this.sku,
    this.price,
    this.regularPrice,
    this.salePrice,
    this.dateOnSaleFrom,
    this.dateOnSaleTo,
    this.onSale,
    this.status,
    this.purchasable,
    this.virtual,
    this.downloadable,
    this.taxStatus,
    this.taxClass,
    this.manageStock,
    this.stockQuantity,
    this.stockStatus,
    this.backorders,
    this.backordersAllowed,
    this.backordered,
    this.weight,
    this.shippingClass,
    this.image,
    required this.attributes,
    this.menuOrder,
    required this.metaData,
  });

  // Helper function to safely convert dynamic values to bool
  static bool? _safeBool(dynamic value) {
    if (value == null) return null;
    if (value is bool) return value;
    if (value is String) {
      if (value.toLowerCase() == 'true' || value == '1') return true;
      if (value.toLowerCase() == 'false' || value == '0') return false;
      return null;
    }
    if (value is int) return value != 0;
    return null;
  }

  factory MyProductVariation.fromJson(Map<String, dynamic> json) {
    return MyProductVariation(
      id: json['id'] ?? 0,
      dateCreated: json['date_created'] != null ? DateTime.tryParse(json['date_created']) : null,
      dateModified: json['date_modified'] != null ? DateTime.tryParse(json['date_modified']) : null,
      description: json['description']?.toString(),
      permalink: json['permalink']?.toString(),
      sku: json['sku']?.toString(),
      price: json['price']?.toString(),
      regularPrice: json['regular_price']?.toString(),
      salePrice: json['sale_price']?.toString(),
      dateOnSaleFrom: json['date_on_sale_from'] != null ? DateTime.tryParse(json['date_on_sale_from']) : null,
      dateOnSaleTo: json['date_on_sale_to'] != null ? DateTime.tryParse(json['date_on_sale_to']) : null,
      onSale: _safeBool(json['on_sale']),
      status: json['status']?.toString(),
      purchasable: _safeBool(json['purchasable']),
      virtual: _safeBool(json['virtual']),
      downloadable: _safeBool(json['downloadable']),
      taxStatus: json['tax_status']?.toString(),
      taxClass: json['tax_class']?.toString(),
      manageStock: _safeBool(json['manage_stock']),
      stockQuantity: json['stock_quantity'] as int?,
      stockStatus: json['stock_status']?.toString(),
      backorders: json['backorders']?.toString(),
      backordersAllowed: _safeBool(json['backorders_allowed']),
      backordered: _safeBool(json['backordered']),
      weight: json['weight']?.toString(),
      shippingClass: json['shipping_class']?.toString(),
      image: json['image'] != null ? MyProductImage.fromJson(json['image']) : null,
      attributes: (json['attributes'] as List?)?.map((attr) {
        try {
          return MyProductVariationAttribute.fromJson(attr);
        } catch (e) {
          print('⚠️ Failed to parse variation attribute: $e');
          // Create a safe fallback attribute
          return MyProductVariationAttribute(
            id: attr['id'] ?? 0,
            name: attr['name']?.toString() ?? '',
            option: attr['option']?.toString() ?? '',
          );
        }
      }).toList() ?? [],
      menuOrder: json['menu_order'] as int?,
      metaData: [], // Will be populated if needed
    );
  }

  factory MyProductVariation.fromWooProductVariation(WooProductVariation wooVariation) {
    return MyProductVariation(
      id: wooVariation.id ?? 0,
      dateCreated: wooVariation.dateCreated,
      dateModified: wooVariation.dateModified,
      description: wooVariation.description,
      permalink: wooVariation.permalink,
      sku: wooVariation.sku,
      price: wooVariation.price?.toString(),
      regularPrice: wooVariation.regularPrice?.toString(),
      salePrice: wooVariation.salePrice?.toString(),
      dateOnSaleFrom: wooVariation.dateOnSaleFrom,
      dateOnSaleTo: wooVariation.dateOnSaleTo,
      onSale: wooVariation.onSale,
      status: wooVariation.status?.name,
      purchasable: wooVariation.purchasable,
      virtual: wooVariation.virtual,
      downloadable: wooVariation.downloadable,
      taxStatus: wooVariation.taxStatus?.name,
      taxClass: wooVariation.taxClass,
      manageStock: wooVariation.manageStock,
      stockQuantity: wooVariation.stockQuantity,
      stockStatus: wooVariation.stockStatus?.name,
      backorders: wooVariation.backorders?.name,
      backordersAllowed: wooVariation.backordersAllowed,
      backordered: wooVariation.backordered,
      weight: wooVariation.weight,
      shippingClass: wooVariation.shippingClass,
      image: wooVariation.image != null ? MyProductImage.fromWooProductImage(wooVariation.image!) : null,
      attributes: wooVariation.attributes.map((attr) => MyProductVariationAttribute.fromWooProductVariationAttribute(attr)).toList(),
      menuOrder: wooVariation.menuOrder,
      metaData: wooVariation.metaData,
    );
  }

  // Helper methods
  String getSafePrice({String fallback = '0.00'}) {
    return price ?? fallback;
  }

  String getSafeRegularPrice({String fallback = '0.00'}) {
    return regularPrice ?? fallback;
  }

  String getSafeSalePrice({String fallback = '0.00'}) {
    return salePrice ?? fallback;
  }

  bool isInStock() {
    return stockStatus == 'instock';
  }

  bool isOnSale() {
    return onSale ?? false;
  }
}
