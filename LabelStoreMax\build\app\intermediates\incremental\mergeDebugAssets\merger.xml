<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.stripe:stripe-3ds2-android:6.2.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\assets"><file name="ds-amex.pem" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\assets\ds-amex.pem"/><file name="ds-cartesbancaires.pem" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\assets\ds-cartesbancaires.pem"/><file name="ds-discover.cer" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\assets\ds-discover.cer"/><file name="ds-mastercard.crt" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\assets\ds-mastercard.crt"/><file name="ds-test-ec.txt" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\assets\ds-test-ec.txt"/><file name="ds-test-rsa.txt" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\assets\ds-test-rsa.txt"/><file name="ds-visa.crt" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\assets\ds-visa.crt"/></source></dataSet><dataSet config="com.stripe:payments-core:21.6.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\assets"><file name="au_becs_bsb.json" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\assets\au_becs_bsb.json"/></source></dataSet><dataSet config=":sqflite_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\sqflite_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":sign_in_with_apple" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\sign_in_with_apple\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":path_provider_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\path_provider_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":google_sign_in_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_sign_in_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":geocoding_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geocoding_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":fluttertoast" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\fluttertoast\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":razorpay_flutter" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\razorpay_flutter\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_secure_storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_secure_storage\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_plugin_android_lifecycle" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_plugin_android_lifecycle\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":google_maps_flutter_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_maps_flutter_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_inappwebview_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":firebase_core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":firebase_messaging" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":url_launcher_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":image_picker_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_local_notifications" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":geolocator_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_facebook_auth" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_facebook_auth\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":stripe_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\stripe_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":shared_preferences_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\shared_preferences_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":package_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\package_info_plus\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_timezone" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_timezone\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":app_badge_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>