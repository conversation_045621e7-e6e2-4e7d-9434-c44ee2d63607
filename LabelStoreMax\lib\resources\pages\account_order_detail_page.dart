//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '/bootstrap/helpers.dart';
import '/app/services/woocommerce_service.dart';
import '/resources/widgets/safearea_widget.dart';
import '/resources/widgets/velvete_ui.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';

class AccountOrderDetailPage extends NyStatefulWidget {
  static RouteView path =
      ("/account-order-detail", (_) => AccountOrderDetailPage());

  AccountOrderDetailPage({super.key})
      : super(child: () => _AccountOrderDetailPageState());
}

class _AccountOrderDetailPageState extends NyPage<AccountOrderDetailPage> {
  int? _orderId;
  WooOrder? _order;

  WooOrderStatus _parseStatus(String? status) {
    switch (status?.toLowerCase()) {
      case 'processing':
        return WooOrderStatus.processing;
      case 'completed':
        return WooOrderStatus.completed;
      case 'on-hold':
        return WooOrderStatus.onHold;
      case 'pending':
        return WooOrderStatus.pending;
      case 'cancelled':
        return WooOrderStatus.cancelled;
      case 'refunded':
        return WooOrderStatus.refunded;
      case 'failed':
        return WooOrderStatus.failed;
      default:
        return WooOrderStatus.processing;
    }
  }

  @override
  get init => () async {
        _orderId = widget.controller.data();
        await _fetchOrder();
      };

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: Container(
          margin: EdgeInsets.only(left: 0),
          child: IconButton(
            icon: Icon(Icons.arrow_back_ios),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        title: afterNotNull(_orderId,
            child: () =>
                Text("${trans("Order").capitalize()} #${_orderId.toString()}"),
            loading: CupertinoActivityIndicator()),
        centerTitle: true,
      ),
      resizeToAvoidBottomInset: false,
      body: SafeAreaWidget(
        child: afterLoad(
          child: () => Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Padding(
                padding: EdgeInsets.only(top: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "${trans("Date Ordered").capitalize()}: ${_order?.dateCreated != null ? dateFormatted(
                        date: _order!.dateCreated!.toIso8601String(),
                        formatType: formatForDateTime(FormatType.date),
                      ) : ""}",
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: match(
                            _order?.status,
                            () => {
                                  "completed": Colors.green,
                                  "processing": Colors.orange,
                                  "cancelled": Colors.red,
                                  "refunded": Colors.red,
                                  "failed": Colors.red,
                                  "on-hold": Colors.orange,
                                  "pending": Colors.orange,
                                },
                            defaultValue: Colors.grey),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        (_order?.status?.name ?? "").capitalize(),
                        style: TextStyle(
                            fontSize: 12, fontWeight: FontWeight.w600),
                      ),
                    )
                  ],
                ).paddingSymmetric(horizontal: 16),
              ),
              Container(
                margin: EdgeInsets.only(top: 10, bottom: 10),
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: (Theme.of(context).brightness == Brightness.light)
                      ? wsBoxShadow()
                      : null,
                  color: (Theme.of(context).brightness == Brightness.light)
                      ? Colors.white
                      : Color(0xFF2C2C2C),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Flexible(
                      child: Text("${trans("Ships to").capitalize()}:"),
                    ),
                    Flexible(
                      child: Text(
                        [
                          [
                            _order?.shipping?.firstName,
                            _order?.shipping?.lastName
                          ].where((t) => t != null).toList().join(" "),
                          _order?.shipping?.address1,
                          _order?.shipping?.address2,
                          _order?.shipping?.city,
                          _order?.shipping?.state,
                          _order?.shipping?.postcode,
                          _order?.shipping?.country,
                        ]
                            .where((t) => (t != "" && t != null))
                            .toList()
                            .join("\n"),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: ListView.builder(
                  itemBuilder: (cxt, i) {
                    WooLineItem lineItem = _order!.lineItems![i];
                    return Card(
                      child: ListTile(
                        contentPadding: EdgeInsets.only(
                            top: 5, bottom: 5, left: 8, right: 6),
                        title: Container(
                          decoration: BoxDecoration(
                            border: Border(
                              bottom: BorderSide(
                                  color: Color(0xFFFCFCFC), width: 1),
                            ),
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: <Widget>[
                              Flexible(
                                child: Text(
                                  lineItem.name!,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              Container(
                                width: 70,
                                alignment: Alignment.topRight,
                                child: Text(
                                  formatStringCurrency(total: lineItem.total?.toString())
                                      .capitalize(),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                              ),
                            ],
                          ),
                        ),
                        subtitle: Container(
                          decoration: BoxDecoration(
                              border: Border(
                                  top: BorderSide(color: Colors.grey[100]!))),
                          padding: const EdgeInsets.only(top: 10),
                          margin: EdgeInsets.only(top: 4),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: <Widget>[
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: <Widget>[
                                  Text(
                                    formatStringCurrency(
                                      total: lineItem.price?.toString(),
                                    ),
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium!
                                        .copyWith(
                                          fontWeight: FontWeight.w600,
                                        ),
                                    textAlign: TextAlign.left,
                                  ),
                                  Text(
                                    "x ${lineItem.quantity.toString()}",
                                    style:
                                        Theme.of(context).textTheme.bodyLarge,
                                    textAlign: TextAlign.left,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                  itemCount: _order?.lineItems?.length ?? 0,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _fetchOrder() async {
    if (_orderId == null) {
      print('❌ No order ID provided');
      return;
    }

    print("🔍 Fetching details for order ID: $_orderId");

    try {
      // Fetch the raw order data directly, bypassing the flawed service method.
      final response = await WooCommerceService().wooCommerce.dio.get('/orders/$_orderId');

      if (response.data is Map<String, dynamic>) {
        final Map<String, dynamic> rawOrder = response.data as Map<String, dynamic>;

        // Manually parse the raw map into a WooOrder object.
        _order = WooOrder(
          id: rawOrder['id'],
          number: rawOrder['number'],
          status: _parseStatus(rawOrder['status']), // Use the safe parser
          dateCreated: DateTime.tryParse(rawOrder['date_created_gmt'] ?? ''),
          total: double.tryParse(rawOrder['total']?.toString() ?? '0'),
          billing: rawOrder['billing'] != null ? WooBilling.fromJson(rawOrder['billing']) : null,
          shipping: rawOrder['shipping'] != null ? WooShipping.fromJson(rawOrder['shipping']) : null,
          lineItems: (rawOrder['line_items'] as List).map((item) {
            // Manually parse each line item to prevent crashes, just like in the list view.
            return WooLineItem(
              id: item['id'],
              name: item['name'],
              productId: item['product_id'],
              variationId: item['variation_id'],
              quantity: item['quantity'],
              subtotal: double.tryParse(item['subtotal']?.toString() ?? '0.00'),
              total: double.tryParse(item['total']?.toString() ?? '0.00'),
              sku: item['sku'] ?? '',
              price: (item['price'] as num?)?.toDouble() ?? 0.0,
            );
          }).toList(),
        );
        print("✅ Successfully fetched and parsed order details for ID: $_orderId");
        setState(() {}); // Refresh UI with order data
      } else {
        print("❌ Fetched raw order data is not a Map: ${response.data}");
        _order = null; // Ensure _order is null if data format is unexpected
        showToast(
          title: trans("Error"),
          description: trans("Invalid order data format"),
          style: ToastNotificationStyleType.danger,
        );
      }
    } catch (e, stackTrace) {
      print("❌ CRITICAL ERROR fetching order details for ID: $_orderId");
      print("   Error: $e");
      print("   Stack Trace: $stackTrace");
      // Ensure _order is null on failure so the UI shows an error state.
      _order = null;

      // Show error message to user
      showToast(
        title: trans("Error"),
        description: trans("Failed to load order details. Please try again."),
        style: ToastNotificationStyleType.danger,
      );
    }
  }
}
