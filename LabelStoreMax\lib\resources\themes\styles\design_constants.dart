import 'package:flutter/material.dart';

/// Design Constants for "Out of This World" Aesthetic
/// Centralized design system values for consistent UI/UX
class DesignConstants {
  // === CORNER RADII - Sophisticated Roundness ===
  static const double cardRadius = 16.0;
  static const double buttonRadius = 12.0;
  static const double inputRadius = 12.0;
  static const double imageRadius = 14.0;
  static const double modalRadius = 20.0;
  static const double chipRadius = 20.0;

  // === SHADOWS - Subtle Depth & Elevation ===
  
  // Card Shadows - Barely perceptible but provide depth
  static List<BoxShadow> get cardShadow => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.08),
      blurRadius: 20,
      offset: const Offset(0, 8),
      spreadRadius: 0,
    ),
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.04),
      blurRadius: 6,
      offset: const Offset(0, 2),
      spreadRadius: 0,
    ),
  ];

  // Button Shadows - Subtle elevation for CTAs
  static List<BoxShadow> get buttonShadow => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.12),
      blurRadius: 16,
      offset: const Offset(0, 4),
      spreadRadius: 0,
    ),
  ];

  // App Bar Shadow - Minimal but present
  static List<BoxShadow> get appBarShadow => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.06),
      blurRadius: 12,
      offset: const Offset(0, 2),
      spreadRadius: 0,
    ),
  ];

  // Bottom Navigation Shadow - Upward elevation
  static List<BoxShadow> get bottomNavShadow => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.08),
      blurRadius: 16,
      offset: const Offset(0, -4),
      spreadRadius: 0,
    ),
  ];

  // === SPACING - Luxurious Airiness ===
  static const double baseHorizontalPadding = 24.0;
  static const double baseVerticalPadding = 20.0;
  static const double sectionSpacing = 32.0;
  static const double itemSpacing = 16.0;
  static const double compactSpacing = 8.0;
  static const double generousSpacing = 40.0;

  // === ANIMATION DURATIONS ===
  static const Duration fastAnimation = Duration(milliseconds: 200);
  static const Duration normalAnimation = Duration(milliseconds: 300);
  static const Duration slowAnimation = Duration(milliseconds: 500);

  // === ANIMATION CURVES ===
  static const Curve elegantCurve = Curves.easeOutCubic;
  static const Curve bouncyCurve = Curves.elasticOut;
  static const Curve smoothCurve = Curves.decelerate;

  // === TYPOGRAPHY SCALE ===
  static const double displayLarge = 32.0;
  static const double displayMedium = 28.0;
  static const double displaySmall = 24.0;
  static const double headlineLarge = 22.0;
  static const double headlineMedium = 20.0;
  static const double headlineSmall = 18.0;
  static const double bodyLarge = 16.0;
  static const double bodyMedium = 14.0;
  static const double bodySmall = 12.0;
  static const double labelLarge = 14.0;
  static const double labelMedium = 12.0;
  static const double labelSmall = 10.0;

  // === ICON SIZES ===
  static const double iconSmall = 16.0;
  static const double iconMedium = 24.0;
  static const double iconLarge = 32.0;
  static const double iconXLarge = 48.0;

  // === BUTTON HEIGHTS ===
  static const double buttonHeightSmall = 40.0;
  static const double buttonHeightMedium = 48.0;
  static const double buttonHeightLarge = 56.0;

  // === HELPER METHODS ===
  
  /// Get card decoration with consistent styling
  static BoxDecoration cardDecoration({
    required Color backgroundColor,
    bool isDark = false,
  }) {
    return BoxDecoration(
      color: backgroundColor,
      borderRadius: BorderRadius.circular(cardRadius),
      boxShadow: isDark ? _darkCardShadow : cardShadow,
    );
  }

  /// Dark theme card shadows
  static List<BoxShadow> get _darkCardShadow => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.3),
      blurRadius: 20,
      offset: const Offset(0, 8),
      spreadRadius: 0,
    ),
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.15),
      blurRadius: 6,
      offset: const Offset(0, 2),
      spreadRadius: 0,
    ),
  ];

  /// Get button decoration with consistent styling
  static BoxDecoration buttonDecoration({
    required Color backgroundColor,
    bool isOutlined = false,
    Color? borderColor,
  }) {
    return BoxDecoration(
      color: backgroundColor,
      borderRadius: BorderRadius.circular(buttonRadius),
      border: isOutlined && borderColor != null
          ? Border.all(color: borderColor, width: 1.5)
          : null,
      boxShadow: isOutlined ? null : buttonShadow,
    );
  }

  /// Get input decoration with consistent styling
  static InputDecoration inputDecoration({
    required String hintText,
    required Color borderColor,
    required Color focusColor,
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) {
    return InputDecoration(
      hintText: hintText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(inputRadius),
        borderSide: BorderSide(color: borderColor),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(inputRadius),
        borderSide: BorderSide(color: borderColor),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(inputRadius),
        borderSide: BorderSide(color: focusColor, width: 2),
      ),
      contentPadding: const EdgeInsets.symmetric(
        horizontal: baseHorizontalPadding,
        vertical: baseVerticalPadding,
      ),
    );
  }
}
