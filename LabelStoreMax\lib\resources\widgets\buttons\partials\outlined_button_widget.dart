import 'package:flutter/material.dart';
import '/bootstrap/extensions.dart';
import '/resources/widgets/buttons/abstract/app_button.dart';
import '/resources/themes/styles/design_constants.dart';

class OutlinedButton extends AppButton {
  final Color? borderColor;
  final Color? textColor;

  const OutlinedButton({
    super.key,
    required super.text,
    super.onPressed,
    this.borderColor,
    this.textColor,
    super.width,
    super.height,
  });

  @override
  Widget build(BuildContext context) {
    return _AnimatedOutlinedButton(
      onPressed: onPressed,
      borderColor: borderColor ?? context.color.buttonBackground,
      textColor: textColor ?? context.color.content,
      child: buildButtonChild(
        context,
        textColor: textColor ?? context.color.content,
        backgroundColor: Colors.transparent,
      ),
    );
  }
}

/// Enhanced Outlined Button with sophisticated micro-interactions
class _AnimatedOutlinedButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final Color borderColor;
  final Color textColor;
  final Widget child;

  const _AnimatedOutlinedButton({
    required this.onPressed,
    required this.borderColor,
    required this.textColor,
    required this.child,
  });

  @override
  State<_AnimatedOutlinedButton> createState() => _AnimatedOutlinedButtonState();
}

class _AnimatedOutlinedButtonState extends State<_AnimatedOutlinedButton>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _borderController;
  late AnimationController _fillController;

  late Animation<double> _scaleAnimation;
  late Animation<double> _borderAnimation;
  late Animation<double> _fillAnimation;

  @override
  void initState() {
    super.initState();

    // Scale animation for press feedback
    _scaleController = AnimationController(
      duration: DesignConstants.fastAnimation,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.97,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: DesignConstants.elegantCurve,
    ));

    // Border animation for elegant outline effect
    _borderController = AnimationController(
      duration: DesignConstants.normalAnimation,
      vsync: this,
    );
    _borderAnimation = Tween<double>(
      begin: 2.0,
      end: 3.0,
    ).animate(CurvedAnimation(
      parent: _borderController,
      curve: DesignConstants.smoothCurve,
    ));

    // Fill animation for subtle background fill on press
    _fillController = AnimationController(
      duration: DesignConstants.fastAnimation,
      vsync: this,
    );
    _fillAnimation = Tween<double>(
      begin: 0.0,
      end: 0.08,
    ).animate(CurvedAnimation(
      parent: _fillController,
      curve: DesignConstants.elegantCurve,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _borderController.dispose();
    _fillController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.onPressed != null) {
      _scaleController.forward();
      _borderController.forward();
      _fillController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    _resetAnimations();
  }

  void _handleTapCancel() {
    _resetAnimations();
  }

  void _resetAnimations() {
    _scaleController.reverse();
    _borderController.reverse();
    _fillController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      onTap: widget.onPressed,
      child: AnimatedBuilder(
        animation: Listenable.merge([
          _scaleAnimation,
          _borderAnimation,
          _fillAnimation,
        ]),
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              decoration: DesignConstants.buttonDecoration(
                backgroundColor: widget.borderColor.withValues(alpha: _fillAnimation.value),
                isOutlined: true,
                borderColor: widget.borderColor,
              ).copyWith(
                border: Border.all(
                  color: widget.borderColor,
                  width: _borderAnimation.value,
                ),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(DesignConstants.buttonRadius),
                  onTap: () {}, // Handled by GestureDetector
                  child: widget.child,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
