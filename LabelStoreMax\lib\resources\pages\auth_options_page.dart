//  Velvete Store
//
//  Created by Augment Agent.
//  2025, Velvete Store. All rights reserved.
//

import 'package:flutter/material.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/resources/pages/account_login_page.dart';
import '/resources/pages/account_register_page.dart';
import '/resources/pages/home_page.dart';
import '/app/services/social_login_service.dart';

class AuthOptionsPage extends NyStatefulWidget {
  static RouteView path = ("/auth-options", (_) => AuthOptionsPage());

  AuthOptionsPage({super.key}) : super(child: () => _AuthOptionsPageState());
}

class _AuthOptionsPageState extends NyPage<AuthOptionsPage> {
  final SocialLoginService _socialLoginService = SocialLoginService();

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Safe<PERSON>rea(
        child: Padding(
          padding: EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo
              Container(
                height: 120,
                width: 120,
                decoration: BoxDecoration(
                  color: Color(0xFFB76E79).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(60),
                ),
                child: Icon(
                  Icons.store,
                  size: 60,
                  color: Color(0xFFB76E79),
                ),
              ),
              
              SizedBox(height: 32),
              
              // Welcome text
              Text(
                'مرحباً بك في فيلفيت',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFFB76E79),
                ),
              ),
              
              SizedBox(height: 16),
              
              Text(
                'اختر طريقة الدخول المناسبة لك',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                ),
              ),
              
              SizedBox(height: 48),
              
              // Login button
              SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: () {
                    routeTo(AccountLoginPage.path);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(0xFFB76E79),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(28),
                    ),
                    elevation: 2,
                  ),
                  child: Text(
                    'تسجيل الدخول',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              
              SizedBox(height: 16),
              
              // Create account button
              SizedBox(
                width: double.infinity,
                height: 56,
                child: OutlinedButton(
                  onPressed: () {
                    routeTo(AccountRegistrationPage.path);
                  },
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: Color(0xFFB76E79), width: 2),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(28),
                    ),
                  ),
                  child: Text(
                    'إنشاء حساب جديد',
                    style: TextStyle(
                      color: Color(0xFFB76E79),
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              
              SizedBox(height: 24),
              
              // Divider
              Row(
                children: [
                  Expanded(child: Divider(color: Colors.grey[300])),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: Text(
                      'أو',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ),
                  Expanded(child: Divider(color: Colors.grey[300])),
                ],
              ),
              
              SizedBox(height: 24),
              
              // Browse as guest button
              SizedBox(
                width: double.infinity,
                height: 56,
                child: TextButton(
                  onPressed: () {
                    routeTo(HomePage.path, navigationType: NavigationType.pushReplace);
                  },
                  style: TextButton.styleFrom(
                    backgroundColor: Colors.grey[100],
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(28),
                    ),
                  ),
                  child: Text(
                    'تصفح كضيف',
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              
              SizedBox(height: 32),

              // Social login section
              Text(
                'أو سجل الدخول باستخدام',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                ),
              ),

              SizedBox(height: 20),

              // Social login buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Facebook login
                  _buildSocialLoginButton(
                    onTap: _handleFacebookLogin,
                    color: Color(0xFF1877F2),
                    icon: Icons.facebook,
                    label: 'Facebook',
                  ),

                  SizedBox(width: 20),

                  // Google login
                  _buildSocialLoginButton(
                    onTap: _handleGoogleLogin,
                    color: Color(0xFFDB4437),
                    icon: Icons.g_mobiledata,
                    label: 'Google',
                  ),

                  SizedBox(width: 20),

                  // Apple login (iOS only)
                  _buildSocialLoginButton(
                    onTap: _handleAppleLogin,
                    color: Colors.black,
                    icon: Icons.apple,
                    label: 'Apple',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSocialLoginButton({
    required VoidCallback onTap,
    required Color color,
    required IconData icon,
    required String label,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: color,
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 28,
        ),
      ),
    );
  }

  void _handleFacebookLogin() async {
    try {
      showToast(
        title: 'جاري تسجيل الدخول...',
        description: 'يرجى الانتظار',
        style: ToastNotificationStyleType.info,
      );

      SocialLoginResult result = await _socialLoginService.signInWithFacebook();

      if (result.success) {
        showToast(
          title: 'مرحباً ${result.firstName}!',
          description: 'تم تسجيل الدخول بنجاح',
          style: ToastNotificationStyleType.success,
        );

        // Navigate to home page
        routeTo(HomePage.path, navigationType: NavigationType.pushReplace);
      } else if (!result.cancelled) {
        showToast(
          title: 'خطأ في تسجيل الدخول',
          description: result.errorMessage ?? 'فشل في تسجيل الدخول',
          style: ToastNotificationStyleType.danger,
        );
      }
    } catch (e) {
      showToast(
        title: 'خطأ',
        description: 'حدث خطأ غير متوقع',
        style: ToastNotificationStyleType.danger,
      );
    }
  }

  void _handleGoogleLogin() async {
    try {
      showToast(
        title: 'جاري تسجيل الدخول...',
        description: 'يرجى الانتظار',
        style: ToastNotificationStyleType.info,
      );

      SocialLoginResult result = await _socialLoginService.signInWithGoogle();

      if (result.success) {
        showToast(
          title: 'مرحباً ${result.firstName}!',
          description: 'تم تسجيل الدخول بنجاح',
          style: ToastNotificationStyleType.success,
        );

        // Navigate to home page
        routeTo(HomePage.path, navigationType: NavigationType.pushReplace);
      } else if (!result.cancelled) {
        showToast(
          title: 'خطأ في تسجيل الدخول',
          description: result.errorMessage ?? 'فشل في تسجيل الدخول',
          style: ToastNotificationStyleType.danger,
        );
      }
    } catch (e) {
      showToast(
        title: 'خطأ',
        description: 'حدث خطأ غير متوقع',
        style: ToastNotificationStyleType.danger,
      );
    }
  }

  void _handleAppleLogin() async {
    try {
      showToast(
        title: 'جاري تسجيل الدخول...',
        description: 'يرجى الانتظار',
        style: ToastNotificationStyleType.info,
      );

      SocialLoginResult result = await _socialLoginService.signInWithApple();

      if (result.success) {
        showToast(
          title: 'مرحباً ${result.firstName}!',
          description: 'تم تسجيل الدخول بنجاح',
          style: ToastNotificationStyleType.success,
        );

        // Navigate to home page
        routeTo(HomePage.path, navigationType: NavigationType.pushReplace);
      } else if (!result.cancelled) {
        showToast(
          title: 'خطأ في تسجيل الدخول',
          description: result.errorMessage ?? 'فشل في تسجيل الدخول',
          style: ToastNotificationStyleType.danger,
        );
      }
    } catch (e) {
      showToast(
        title: 'خطأ',
        description: 'حدث خطأ غير متوقع',
        style: ToastNotificationStyleType.danger,
      );
    }
  }
}
