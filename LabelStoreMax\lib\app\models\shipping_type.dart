//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import '/bootstrap/helpers.dart';

// Simple shipping method classes to replace WooSignal equivalents
class FlatRate {
  String? title;
  String? methodId;
  String? cost;
  String? classCost;
  List<ShippingClasses>? shippingClasses;

  FlatRate({this.title, this.methodId, this.cost, this.classCost, this.shippingClasses});
}

class FreeShipping {
  String? title;
  String? methodId;
  String? cost;
  String? id;
  String? minimumOrderAmount;

  FreeShipping({this.title, this.methodId, this.cost, this.id, this.minimumOrderAmount});
}

class LocalPickup {
  String? title;
  String? methodId;
  String? cost;

  LocalPickup({this.title, this.methodId, this.cost});
}

class ShippingClasses {
  String? id;
  String? cost;

  ShippingClasses({this.id, this.cost});
}

class CustomShippingMethod {
  String? title;
  String? methodId;
  String? cost;
  String? description;

  CustomShippingMethod({this.title, this.methodId, this.cost, this.description});
}

class ShippingType {
  String? methodId;
  String cost;
  String? minimumValue;
  dynamic object;

  ShippingType(
      {required this.methodId,
      this.object,
      required this.cost,
      required this.minimumValue});

  /// Create ShippingType from dynamic WooCommerce shipping method
  factory ShippingType.fromDynamicMethod(dynamic shippingMethod) {
    print('🔍 ===== SHIPPING TYPE FROM DYNAMIC METHOD =====');
    print('🔍 Input type: ${shippingMethod.runtimeType}');
    print('🔍 Input data: $shippingMethod');

    // Handle both ShippingMethod objects and Map data
    if (shippingMethod is Map<String, dynamic>) {
      final cost = shippingMethod['cost']?.toString() ?? '0';
      print('🔍 Map-based cost: $cost (from ${shippingMethod['cost']})');

      final result = ShippingType(
        methodId: shippingMethod['method_id'] ?? shippingMethod['methodId'],
        cost: cost,
        minimumValue: shippingMethod['minimum_amount']?.toString(),
        object: _createShippingObject(shippingMethod),
      );

      print('🔍 Created ShippingType with cost: ${result.cost}');
      print('🔍 ===============================================');
      return result;
    } else {
      // Assume it's a ShippingMethod object
      final cost = shippingMethod.cost;
      print('🔍 Object-based cost: $cost');

      final result = ShippingType(
        methodId: shippingMethod.methodId,
        cost: cost,
        minimumValue: shippingMethod.minimumAmount,
        object: _createShippingObjectFromMethod(shippingMethod),
      );

      print('🔍 Created ShippingType with cost: ${result.cost}');
      print('🔍 ===============================================');
      return result;
    }
  }

  /// Create appropriate shipping object based on method ID
  static dynamic _createShippingObject(Map<String, dynamic> method) {
    String methodId = method['method_id'] ?? method['methodId'] ?? '';
    String title = method['title'] ?? method['method_title'] ?? '';
    String cost = method['cost']?.toString() ?? '0';

    switch (methodId.toLowerCase()) {
      case 'flat_rate':
        return FlatRate(
          title: title,
          methodId: methodId,
          cost: cost,
        );
      case 'free_shipping':
        return FreeShipping(
          title: title,
          methodId: methodId,
          cost: cost,
          minimumOrderAmount: method['minimum_amount']?.toString(),
        );
      case 'local_pickup':
        return LocalPickup(
          title: title,
          methodId: methodId,
          cost: cost,
        );
      default:
        // For custom shipping methods like "Al-sabil", create a generic object
        return CustomShippingMethod(
          title: title,
          methodId: methodId,
          cost: cost,
        );
    }
  }

  /// Create shipping object from ShippingMethod object
  static dynamic _createShippingObjectFromMethod(dynamic shippingMethod) {
    String methodId = shippingMethod.methodId;
    String title = shippingMethod.title;
    String cost = shippingMethod.cost;

    switch (methodId.toLowerCase()) {
      case 'flat_rate':
        return FlatRate(
          title: title,
          methodId: methodId,
          cost: cost,
        );
      case 'free_shipping':
        return FreeShipping(
          title: title,
          methodId: methodId,
          cost: cost,
          minimumOrderAmount: shippingMethod.minimumAmount,
        );
      case 'local_pickup':
        return LocalPickup(
          title: title,
          methodId: methodId,
          cost: cost,
        );
      default:
        // For custom shipping methods like "Al-sabil"
        return CustomShippingMethod(
          title: title,
          methodId: methodId,
          cost: cost,
        );
    }
  }

  Map<String, dynamic> toJson() => {
        'methodId': methodId,
        'object': object,
        'cost': cost,
        'minimumValue': minimumValue
      };

  String? getTotal({bool withFormatting = false}) {
    print('🔍 ===== SHIPPING TYPE GET TOTAL DEBUG =====');
    print('🔍 Method ID: $methodId');
    print('🔍 Cost: $cost');
    print('🔍 Object: $object');
    print('🔍 Object Type: ${object.runtimeType}');
    print('🔍 With Formatting: $withFormatting');

    if (object != null) {
      switch (methodId) {
        case "flat_rate":
          FlatRate? flatRate = (object as FlatRate?);
          String result = (withFormatting == true
              ? formatStringCurrency(total: cost)
              : flatRate?.cost ?? cost);
          print('🔍 Flat rate result: $result');
          return result;
        case "free_shipping":
          FreeShipping? freeShipping = (object as FreeShipping?);
          String result = (withFormatting == true
              ? formatStringCurrency(total: cost)
              : freeShipping?.cost ?? cost);
          print('🔍 Free shipping result: $result');
          return result;
        case "local_pickup":
          LocalPickup? localPickup = (object as LocalPickup?);
          String result = (withFormatting == true
              ? formatStringCurrency(total: cost)
              : localPickup?.cost ?? cost);
          print('🔍 Local pickup result: $result');
          return result;
        case "v_shipping_by_city":
          // Handle v_shipping_by_city specifically
          String result = (withFormatting == true
              ? formatStringCurrency(total: cost)
              : cost);
          print('🔍 V shipping by city result: $result');
          return result;
        default:
          // Handle custom shipping methods
          if (object is CustomShippingMethod) {
            CustomShippingMethod customMethod = (object as CustomShippingMethod);
            String result = (withFormatting == true
                ? formatStringCurrency(total: cost)
                : customMethod.cost ?? cost);
            print('🔍 Custom shipping method result: $result');
            return result;
          }
          String result = (withFormatting == true
              ? formatStringCurrency(total: cost)
              : cost);
          print('🔍 Default case result: $result');
          return result;
      }
    }
    print('🔍 Object is null, returning "0"');
    return "0";
  }

  String? getTitle() {
    if (object != null) {
      switch (methodId) {
        case "flat_rate":
          FlatRate flatRate = (object as FlatRate);
          return flatRate.title;
        case "free_shipping":
          FreeShipping freeShipping = (object as FreeShipping);
          return freeShipping.title;
        case "local_pickup":
          LocalPickup localPickup = (object as LocalPickup);
          return localPickup.title;
        default:
          // Handle custom shipping methods
          if (object is CustomShippingMethod) {
            CustomShippingMethod customMethod = (object as CustomShippingMethod);
            return customMethod.title;
          }
          return "";
      }
    }
    return "";
  }

  Map<String, dynamic>? toShippingLineFee() {
    print('🔍 ===== CREATING SHIPPING LINE FEE =====');
    print('📦 Method ID: $methodId');
    print('📦 Object Type: ${object.runtimeType}');
    print('📦 Cost: $cost');
    print('📦 Object: $object');

    if (object != null) {
      Map<String, dynamic> tmpShippingLinesObj = {};

      print('🔍 Entering switch statement with methodId: "$methodId"');
      switch (methodId) {
        case "flat_rate":
          FlatRate flatRate = (object as FlatRate);
          tmpShippingLinesObj["method_title"] = flatRate.title;
          tmpShippingLinesObj["method_id"] = flatRate.methodId;
          tmpShippingLinesObj["total"] = cost;
          break;
        case "free_shipping":
          FreeShipping freeShipping = (object as FreeShipping);
          tmpShippingLinesObj["method_title"] = freeShipping.title;
          tmpShippingLinesObj["method_id"] = freeShipping.methodId;
          tmpShippingLinesObj["total"] = cost;
          break;
        case "local_pickup":
          LocalPickup localPickup = (object as LocalPickup);
          tmpShippingLinesObj["method_title"] = localPickup.title;
          tmpShippingLinesObj["method_id"] = localPickup.methodId;
          tmpShippingLinesObj["total"] = cost;
          break;
        case "v_shipping_by_city":
          // Handle v_shipping_by_city specifically - object is ShippingMethod from dynamic service
          tmpShippingLinesObj["method_title"] = "التوصيل (درب السبيل)";
          tmpShippingLinesObj["method_id"] = methodId;
          tmpShippingLinesObj["total"] = cost;
          print('✅ Created v_shipping_by_city shipping line');
          break;
        default:
          // Handle custom shipping methods
          if (object is CustomShippingMethod) {
            CustomShippingMethod customMethod = (object as CustomShippingMethod);
            tmpShippingLinesObj["method_title"] = customMethod.title;
            tmpShippingLinesObj["method_id"] = customMethod.methodId;
            tmpShippingLinesObj["total"] = cost;
            break;
          }
          // Handle ShippingMethod objects from dynamic shipping service
          if (object.runtimeType.toString() == 'ShippingMethod') {
            tmpShippingLinesObj["method_title"] = object.title ?? "Shipping";
            tmpShippingLinesObj["method_id"] = methodId;
            tmpShippingLinesObj["total"] = cost;
            print('✅ Created shipping line for ShippingMethod object');
            break;
          }
          print('❌ Unknown shipping method type in switch statement: ${object.runtimeType}');
          return null;
      }

      print('✅ Created shipping line fee: $tmpShippingLinesObj');
      return tmpShippingLinesObj;
    }

    print('❌ No shipping object available for toShippingLineFee()');
    return null;
  }
}
