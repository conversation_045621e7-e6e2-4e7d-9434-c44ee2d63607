# Official velvete e-commerce App

# Velvete Store
# Version: 1.2.2
# Author: <PERSON><PERSON><PERSON> <PERSON>.
# Homepage: https://velvete.ly
# Documentation: https://velvete.ly/docs

### Change App Icon
# 1 Replace: public/icon/appicon.png (1024px1024px icon size)
# 2 Run this command from the terminal: "dart run flutter_launcher_icons:main"

### Uploading the IOS/Android app
# IOS https://flutter.dev/docs/deployment/ios
# Android https://flutter.dev/docs/deployment/android

name: flutter_app
description: Label StoreMax

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: '>=3.4.0 <4.0.0'
  flutter: ">=3.24.0 <4.0.0"

dependencies:
  google_fonts: ^6.2.1
  analyzer: ^7.4.5
  intl: ^0.20.2
  nylo_framework: ^6.8.7
  woocommerce_flutter_api: ^1.3.0
  wp_json_api: ^4.3.3
  cached_network_image: ^3.4.1
  package_info_plus: ^8.3.0
  flutter_inappwebview: ^6.1.5
  pull_to_refresh_flutter3: 2.0.2
  url_launcher: ^6.3.1
  bubble_tab_indicator: ^0.1.6
  math_expressions: 2.7.0
  validated: ^2.0.0
  flutter_spinkit: ^5.2.1
  auto_size_text: ^3.0.0
  html: ^0.15.6
  flutter_widget_from_html_core: ^0.16.0
  flutter_rating_bar: ^4.0.1
  flutter_staggered_grid_view: ^0.7.0
  flutter_swiper_view: ^1.1.8
  firebase_messaging: ^15.2.6
  firebase_core: ^3.13.1
  collection: ^1.19.1
  flutter_stripe: ^11.5.0
  razorpay_flutter: ^1.4.0
  pretty_dio_logger: ^1.4.0
  animate_do: ^4.2.0
  google_maps_flutter: ^2.9.0
  geolocator: ^13.0.1
  geocoding: ^3.0.0
  image_picker: ^1.1.2
  http: ^1.2.2
  lottie: ^3.1.2
  # Social Login Dependencies
  google_sign_in: ^6.2.1
  flutter_facebook_auth: ^7.1.1
  sign_in_with_apple: ^6.1.2
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  cupertino_icons: ^1.0.8

dev_dependencies:
  flutter_launcher_icons: ^0.14.3
  lints: ^5.1.1
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

# APP ICON
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "public/app_icon/appicon.png"
  remove_alpha_ios: true

flutter:

  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - public/fonts/
    - public/
    - public/json/
    - public/images/
    - public/animations/
    - lang/
    - .env
